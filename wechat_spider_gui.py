import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import traceback
import requests
import pandas as pd
import time
import os
import json
from datetime import datetime

"""
微信公众号文章爬虫GUI版本
基于wechatfuben.py改进，增加图形界面
日期：2025年1月14日
声明：本工具仅供技术研究，请勿用于非法采集，后果自负。
"""


class WeChatSpider:
    """微信公众号爬虫类"""
    def __init__(self, cookie, token, progress_callback=None):
        """
        初始化爬虫

        :param cookie: 微信公众号平台的Cookie
        :param token: 微信公众号平台的Token
        :param progress_callback: 进度回调函数
        """
        self.session = requests.Session()

        # 设置session超时和重试
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/120.0.0.0 Safari/537.36",
            "Cookie": cookie,
            "Referer": "https://mp.weixin.qq.com/",
        }
        self.base_params = {
            "lang": "zh_CN",
            "f": "json",
            "token": token,
        }
        self.progress_callback = progress_callback

    def test_credentials(self):
        """
        测试Cookie和Token是否有效

        :return: (是否有效, 错误信息)
        """
        try:
            self.log_progress("正在验证Cookie和Token...")

            # 检查基本参数
            cookie_len = len(self.headers.get('Cookie', ''))
            token_len = len(self.base_params.get('token', ''))
            self.log_progress(f"Cookie长度: {cookie_len}, Token长度: {token_len}")

            if cookie_len == 0:
                return False, "Cookie为空"
            if token_len == 0:
                return False, "Token为空"

            test_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
            params = {
                **self.base_params,
                "action": "search_biz",
                "query": "测试",
                "begin": 0,
                "count": 1,
                "ajax": "1",
            }

            self.log_progress("发送验证请求...")
            response = self.session.get(test_url, headers=self.headers, params=params, timeout=15)
            self.log_progress(f"收到响应，状态码: {response.status_code}")

            response.raise_for_status()

            try:
                data = response.json()
                self.log_progress("响应JSON解析成功")
            except ValueError as e:
                self.log_progress(f"JSON解析失败: {str(e)}")
                # 打印响应内容的前200个字符用于调试
                content_preview = response.text[:200] if response.text else "空响应"
                self.log_progress(f"响应内容预览: {content_preview}")
                return False, "响应不是有效的JSON格式，可能Cookie或Token格式错误"

            if 'base_resp' in data:
                ret_code = data['base_resp'].get('ret')
                self.log_progress(f"API返回码: {ret_code}")

                if ret_code == 200013:
                    return False, "Cookie/Token 已过期或无效"
                elif ret_code == -1:
                    return False, "系统繁忙，请稍后重试"
                elif ret_code == 200003:
                    # 权限不足，但凭证可能仍然有效，尝试其他验证方法
                    self.log_progress("搜索API权限不足，尝试其他验证方法...")
                    return self._validate_by_home_page()
                elif ret_code == 0:
                    self.log_progress("✅ Cookie和Token验证成功")
                    return True, "验证成功"
                else:
                    self.log_progress(f"未知错误码: {ret_code}，尝试其他验证方法...")
                    return self._validate_by_home_page()
            else:
                self.log_progress("响应中缺少base_resp字段")
                # 打印完整响应用于调试
                self.log_progress(f"完整响应: {str(data)[:500]}")
                return False, "响应格式异常"

        except requests.exceptions.Timeout:
            self.log_progress("❌ 请求超时")
            return False, "请求超时，请检查网络连接"
        except requests.exceptions.ConnectionError as e:
            self.log_progress(f"❌ 网络连接错误: {str(e)}")
            return False, "网络连接错误，请检查网络设置"
        except requests.exceptions.HTTPError as e:
            self.log_progress(f"❌ HTTP错误: {str(e)}")
            return False, f"HTTP错误: {str(e)}"
        except requests.exceptions.RequestException as e:
            self.log_progress(f"❌ 请求异常: {str(e)}")
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            self.log_progress(f"❌ 未知错误: {str(e)}")
            import traceback
            self.log_progress(f"错误堆栈: {traceback.format_exc()}")
            return False, f"验证过程中发生错误: {str(e)}"

    def _validate_by_home_page(self):
        """通过主页访问验证凭证"""
        try:
            self.log_progress("尝试通过主页验证凭证...")

            home_url = "https://mp.weixin.qq.com/cgi-bin/home"
            params = {
                "t": "home/index",
                "lang": "zh_CN",
                "token": self.base_params.get('token'),
            }

            response = self.session.get(home_url, headers=self.headers, params=params, timeout=15)
            self.log_progress(f"主页响应状态码: {response.status_code}")

            if response.status_code == 302:
                return False, "凭证已过期，需要重新登录"
            elif response.status_code == 200:
                content = response.text

                # 检查是否包含管理页面的关键词
                success_indicators = [
                    "账号信息", "素材管理", "用户管理", "群发功能",
                    "自动回复", "自定义菜单", "统计", "设置"
                ]

                if any(indicator in content for indicator in success_indicators):
                    self.log_progress("✅ 主页验证成功，凭证有效")
                    return True, "验证成功（通过主页验证）"
                elif "请使用微信扫描二维码登录" in content:
                    return False, "需要重新扫码登录"
                elif "系统繁忙" in content:
                    return False, "系统繁忙，请稍后重试"
                else:
                    # 如果主页验证也不确定，尝试更简单的验证
                    return self._validate_by_simple_api()
            else:
                return False, f"主页访问失败，状态码: {response.status_code}"

        except Exception as e:
            self.log_progress(f"主页验证异常: {str(e)}")
            return False, f"主页验证失败: {str(e)}"

    def _validate_by_simple_api(self):
        """通过简单API验证凭证"""
        try:
            self.log_progress("尝试简单API验证...")

            # 使用更简单的API接口
            api_url = "https://mp.weixin.qq.com/cgi-bin/operate_appmsg"
            params = {
                "t": "ajax-response",
                "sub": "list",
                "type": "10",
                "query": "",
                "begin": 0,
                "count": 1,
                "token": self.base_params.get('token'),
                "lang": "zh_CN",
                "f": "json",
                "ajax": "1"
            }

            response = self.session.get(api_url, headers=self.headers, params=params, timeout=15)

            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'base_resp' in data:
                        ret_code = data['base_resp'].get('ret')
                        if ret_code == 0:
                            self.log_progress("✅ 简单API验证成功")
                            return True, "验证成功（通过简单API验证）"
                        elif ret_code == 200013:
                            return False, "凭证已过期"
                        else:
                            # 即使API返回错误，如果能正常响应JSON，说明凭证基本有效
                            self.log_progress("✅ 凭证基本有效（能正常访问API）")
                            return True, "验证成功（凭证基本有效）"
                    else:
                        return False, "API响应格式异常"
                except ValueError:
                    return False, "API响应不是有效JSON"
            else:
                return False, f"简单API访问失败，状态码: {response.status_code}"

        except Exception as e:
            self.log_progress(f"简单API验证异常: {str(e)}")
            return False, f"简单API验证失败: {str(e)}"

    def log_progress(self, message):
        """记录进度信息"""
        if self.progress_callback:
            self.progress_callback(message)
        print(message)

    def refresh_session(self):
        """
        刷新会话，通过访问主页来保持会话活跃
        """
        try:
            self.log_progress("正在刷新会话...")
            main_url = "https://mp.weixin.qq.com/"
            response = self.session.get(main_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                self.log_progress("✅ 会话刷新成功")
                return True
            else:
                self.log_progress(f"⚠️ 会话刷新失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_progress(f"⚠️ 会话刷新异常: {str(e)}")
            return False

    def get_fakeid(self, nickname, begin=0, count=5, retry_count=3):
        """
        根据公众号昵称搜索，获取其fakeid

        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :param retry_count: 重试次数
        :return: 公众号的fakeid
        """
        self.log_progress(f"正在搜索公众号: {nickname}")

        for attempt in range(retry_count):
            if attempt > 0:
                self.log_progress(f"第{attempt + 1}次尝试搜索公众号...")
                # 在重试前刷新会话
                self.refresh_session()
                time.sleep(2)  # 等待2秒

            search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
            params = {
                **self.base_params,
                "action": "search_biz",
                "query": nickname,
                "begin": begin,
                "count": count,
                "ajax": "1",
            }

            try:
                response = self.session.get(search_url, headers=self.headers, params=params, timeout=15)
                response.raise_for_status()

                try:
                    data = response.json()
                except ValueError:
                    if attempt < retry_count - 1:
                        self.log_progress("响应格式异常，准备重试...")
                        continue
                    else:
                        raise Exception("响应不是有效的JSON格式")

                # 检查API响应状态 - 但对于搜索API，200003权限不足不影响数据获取
                if 'base_resp' in data:
                    ret_code = data['base_resp'].get('ret')
                    if ret_code == 200013:
                        if attempt < retry_count - 1:
                            self.log_progress("凭证可能过期，准备重试...")
                            continue
                        else:
                            raise Exception("Cookie/Token 已过期或无效，请重新获取")
                    elif ret_code != 0 and ret_code != 200003:
                        # 200003是搜索权限不足，但不影响数据获取，忽略此错误
                        error_msg = f"搜索失败，错误码: {ret_code}"
                        if attempt < retry_count - 1:
                            self.log_progress(f"{error_msg}，准备重试...")
                            continue
                        else:
                            raise Exception(error_msg)
                    elif ret_code == 200003:
                        self.log_progress("搜索API权限不足，但尝试继续获取数据...")

                # 直接检查数据内容，忽略权限错误
                if "list" in data and data["list"]:
                    for item in data["list"]:
                        if item.get("nickname") == nickname:
                            fakeid = item.get("fakeid")
                            self.log_progress(f"找到公众号 {nickname}，FakeID: {fakeid}")
                            return fakeid

                if attempt < retry_count - 1:
                    self.log_progress(f"未找到公众号 {nickname}，准备重试...")
                    continue
                else:
                    self.log_progress(f"未找到公众号: {nickname}")
                    return None

            except Exception as e:
                if attempt < retry_count - 1:
                    self.log_progress(f"搜索出错: {str(e)}，准备重试...")
                    continue
                else:
                    error_msg = f"获取公众号{nickname}的fakeid失败: {str(e)}"
                    self.log_progress(error_msg)
                    raise Exception(error_msg)

        return None

    def get_articles_page(self, fakeid, begin=0, count=5, retry_count=3):
        """
        获取单页文章，带重试机制

        :param fakeid: 公众号的fakeid
        :param begin: 开始位置
        :param count: 每页数量
        :param retry_count: 重试次数
        :return: (文章列表, 是否还有下一页)
        """
        for attempt in range(retry_count):
            if attempt > 0:
                self.log_progress(f"第{attempt + 1}次尝试获取文章...")
                # 重试前刷新会话并增加等待时间
                self.refresh_session()
                wait_time = min(5 + attempt * 2, 10)  # 递增等待时间，最多10秒
                self.log_progress(f"等待{wait_time}秒后重试...")
                time.sleep(wait_time)

            art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
            params = {
                **self.base_params,
                "query": "",
                "begin": begin,
                "count": count,
                "type": 9,
                "action": "list_ex",
                "fakeid": fakeid,
            }

            try:
                response = self.session.get(art_url, headers=self.headers, params=params, timeout=15)
                response.raise_for_status()

                if response.status_code != 200:
                    if attempt < retry_count - 1:
                        self.log_progress(f"HTTP状态码异常: {response.status_code}，准备重试...")
                        continue
                    else:
                        raise Exception(f"HTTP请求失败，状态码: {response.status_code}")

                try:
                    data = response.json()
                except ValueError as e:
                    if attempt < retry_count - 1:
                        self.log_progress("响应格式异常，准备重试...")
                        continue
                    else:
                        self.log_progress(f"响应内容前500字符: {response.text[:500]}")
                        raise Exception(f"响应不是有效的JSON格式: {str(e)}")

                # 检查API响应状态
                if 'base_resp' in data:
                    ret_code = data['base_resp'].get('ret')
                    self.log_progress(f"API响应状态: {ret_code}")

                    if ret_code == 200013:
                        if attempt < retry_count - 1:
                            self.log_progress("⚠️ 检测到凭证过期，正在尝试恢复...")
                            self.log_progress("💡 提示：这是正常现象，不是被封禁")
                            continue
                        else:
                            self.log_progress("❌ 凭证已完全过期，需要手动更新")
                            self.log_progress("📋 解决方案：")
                            self.log_progress("   1. 打开浏览器访问 https://mp.weixin.qq.com/")
                            self.log_progress("   2. 重新登录微信公众平台")
                            self.log_progress("   3. 按F12获取新的Cookie和Token")
                            self.log_progress("   4. 或者使用凭证助手工具获取")
                            raise Exception("Cookie/Token 已过期，请使用凭证助手重新获取有效凭证")
                    elif ret_code == -1:
                        if attempt < retry_count - 1:
                            self.log_progress("系统繁忙，准备重试...")
                            continue
                        else:
                            raise Exception("系统繁忙，请稍后重试")
                    elif ret_code == 10001:
                        raise Exception("参数错误，请检查FakeID是否正确")
                    elif ret_code != 0:
                        error_msg = f"API返回错误码: {ret_code}, 错误信息: {data['base_resp'].get('err_msg', '未知错误')}"
                        if attempt < retry_count - 1:
                            self.log_progress(f"{error_msg}，准备重试...")
                            continue
                        else:
                            raise Exception(error_msg)

                # 解析文章列表
                if "app_msg_list" in data and data["app_msg_list"]:
                    articles_on_page = data["app_msg_list"]
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link"),
                            "发布时间": datetime.fromtimestamp(item.get("create_time", 0)).strftime("%Y-%m-%d %H:%M:%S") if item.get("create_time") else "未知"
                        }
                        for item in articles_on_page
                    ]
                    has_more = len(articles_on_page) >= count
                    return articles, has_more
                else:
                    # 没有文章列表
                    if begin == 0:  # 第一页就没有文章
                        if attempt < retry_count - 1:
                            self.log_progress("第一页未获取到文章，准备重试...")
                            continue
                        else:
                            self.log_progress(f"完整API响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            raise Exception("未能获取到任何文章，可能是FakeID错误或权限不足")
                    else:
                        return [], False  # 后续页面没有文章，正常结束

            except requests.exceptions.RequestException as e:
                if attempt < retry_count - 1:
                    self.log_progress(f"网络请求失败: {str(e)}，准备重试...")
                    continue
                else:
                    raise Exception(f"网络请求失败: {str(e)}")
            except Exception as e:
                if "Cookie/Token 已过期" in str(e) or "系统繁忙" in str(e) or "参数错误" in str(e) or "未能获取到任何文章" in str(e):
                    if attempt < retry_count - 1 and "参数错误" not in str(e) and "未能获取到任何文章" not in str(e):
                        continue
                    else:
                        raise e
                else:
                    if attempt < retry_count - 1:
                        self.log_progress(f"获取文章出错: {str(e)}，准备重试...")
                        continue
                    else:
                        raise Exception(f"获取文章失败: {str(e)}")

        raise Exception("重试次数已用完，获取文章失败")

    def get_articles(self, fakeid, begin=0, count=5, stop_flag=None, auto_save_callback=None):
        """
        根据fakeid获取公众号的文章列表，并自动翻页

        :param fakeid: 公众号的fakeid
        :param begin: 开始位置
        :param count: 每页数量
        :param stop_flag: 停止标志，用于中断爬取
        :param auto_save_callback: 自动保存回调函数
        :return: 所有文章的列表
        """
        all_articles = []
        page_num = 1
        current_begin = begin
        last_save_count = 0  # 记录上次保存时的文章数量

        while True:
            # 检查停止标志
            if stop_flag and stop_flag():
                self.log_progress("⏹️ 用户停止了爬取，正在保存已获取的文章...")
                break

            self.log_progress(f"正在获取第{page_num}页文章，起始位置: {current_begin}")

            try:
                # 获取单页文章
                articles, has_more = self.get_articles_page(fakeid, current_begin, count)

                if articles:
                    self.log_progress(f"第{page_num}页找到 {len(articles)} 篇文章")
                    all_articles.extend(articles)
                    self.log_progress(f"总共已收集 {len(all_articles)} 篇文章")

                    # 自动保存机制：每50篇文章保存一次
                    if auto_save_callback and len(all_articles) - last_save_count >= 50:
                        try:
                            self.log_progress(f"🔄 自动保存中间结果（{len(all_articles)}篇文章）...")
                            auto_save_callback(all_articles, f"auto_save_{len(all_articles)}")
                            last_save_count = len(all_articles)
                            self.log_progress("✅ 中间结果已保存")
                        except Exception as e:
                            self.log_progress(f"⚠️ 自动保存失败: {str(e)}")
                else:
                    self.log_progress("本页没有找到文章")

                # 检查是否还有下一页
                if not has_more or not articles:
                    self.log_progress("已到达最后一页，抓取结束")
                    break
                else:
                    current_begin += count
                    page_num += 1
                    self.log_progress("准备获取下一页...")

                    # 智能延时：根据已爬取页数调整间隔
                    base_delay = 3  # 基础延时3秒
                    if page_num > 20:  # 超过20页后增加延时
                        extra_delay = min((page_num - 20) * 0.5, 5)  # 最多额外增加5秒
                        total_delay = base_delay + extra_delay
                        self.log_progress(f"智能延时{total_delay:.1f}秒（减少凭证过期风险）...")
                    else:
                        total_delay = base_delay

                    # 在延时期间检查停止标志
                    delay_steps = int(total_delay * 10)  # 分成0.1秒的步骤
                    for _ in range(delay_steps):
                        if stop_flag and stop_flag():
                            self.log_progress("⏹️ 用户停止了爬取，正在保存已获取的文章...")
                            return all_articles
                        time.sleep(0.1)

                    # 每10页刷新一次会话
                    if page_num % 10 == 0:
                        self.log_progress("🔄 定期刷新会话以保持连接...")
                        self.refresh_session()

            except Exception as e:
                self.log_progress(f"获取第{page_num}页文章失败: {str(e)}")

                # 出现异常时，如果已有文章数据，先保存
                if auto_save_callback and all_articles:
                    try:
                        self.log_progress(f"🚨 检测到异常，紧急保存已获取的{len(all_articles)}篇文章...")
                        auto_save_callback(all_articles, f"emergency_save_{len(all_articles)}")
                        self.log_progress("✅ 紧急保存完成")
                    except Exception as save_error:
                        self.log_progress(f"❌ 紧急保存失败: {str(save_error)}")

                raise e

        # 最终保存
        if auto_save_callback and all_articles and len(all_articles) > last_save_count:
            try:
                self.log_progress(f"💾 保存最终结果（{len(all_articles)}篇文章）...")
                auto_save_callback(all_articles, f"final_save_{len(all_articles)}")
                self.log_progress("✅ 最终结果已保存")
            except Exception as e:
                self.log_progress(f"⚠️ 最终保存失败: {str(e)}")

        return all_articles

    def fetch_articles_by_nickname(self, nickname, begin=0, count=5, stop_flag=None, auto_save_callback=None):
        """
        通过公众号昵称直接获取所有文章

        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :param stop_flag: 停止标志
        :param auto_save_callback: 自动保存回调函数
        :return: 所有文章的列表
        """
        fakeid = self.get_fakeid(nickname, begin, count)
        if not fakeid:
            raise ValueError(f"未找到公众号 {nickname} 的 fakeid")
        return self.get_articles(fakeid, stop_flag=stop_flag, auto_save_callback=auto_save_callback)

    def fetch_articles_by_fakeid(self, fakeid, count=5, stop_flag=None, auto_save_callback=None):
        """
        通过FakeID直接获取所有文章

        :param fakeid: 公众号的FakeID
        :param count: 每页数量
        :param stop_flag: 停止标志
        :param auto_save_callback: 自动保存回调函数
        :return: 所有文章的列表
        """
        self.log_progress(f"使用FakeID获取文章: {fakeid}")
        return self.get_articles(fakeid, count=count, stop_flag=stop_flag, auto_save_callback=auto_save_callback)


class WeChatSpiderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微信公众号文章爬虫 - GUI版本 v2.0")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        self.root.minsize(800, 600)

        # 配置文件路径
        self.config_file = "wechat_gui_config.json"

        # 设置样式
        self.setup_styles()

        # 设置窗口居中
        self.center_window()

        # 创建界面
        self.create_widgets()

        # 加载上次保存的配置（在界面创建后）
        self.root.after(100, self.load_config)

        # 爬虫实例
        self.spider = None
        self.is_running = False

        # 文件管理相关变量
        self.cache_file = None  # 缓存文件路径
        self.previous_save_file = None  # 上一个保存的文件路径
        self.current_session_files = []  # 当前会话创建的文件列表

    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()

        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 14, 'bold'))
        style.configure('Subtitle.TLabel', font=('Microsoft YaHei', 10))
        style.configure('Section.TLabel', font=('Microsoft YaHei', 11, 'bold'))
        style.configure('Info.TLabel', font=('Microsoft YaHei', 9), foreground='#666666')
        style.configure('Status.TLabel', font=('Microsoft YaHei', 9))
        style.configure('Accent.TButton', font=('Microsoft YaHei', 10, 'bold'))

        # 配置LabelFrame样式
        style.configure('Card.TLabelframe', padding=10)
        style.configure('Card.TLabelframe.Label', font=('Microsoft YaHei', 10, 'bold'))

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 900
        height = 800
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def load_config(self):
        """加载上次保存的配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 填充Cookie
                if 'cookie' in config and config['cookie']:
                    self.cookie_entry.delete("1.0", tk.END)
                    self.cookie_entry.insert("1.0", config['cookie'])

                # 填充Token
                if 'token' in config and config['token']:
                    self.token_entry.delete(0, tk.END)
                    self.token_entry.insert(0, config['token'])

                # 填充搜索方式
                if 'search_method' in config:
                    self.search_method.set(config['search_method'])

                # 填充公众号昵称/FakeID
                if 'nickname_or_fakeid' in config and config['nickname_or_fakeid']:
                    self.nickname_entry.delete(0, tk.END)
                    self.nickname_entry.insert(0, config['nickname_or_fakeid'])

                # 填充输出目录
                if 'output_dir' in config and config['output_dir']:
                    self.output_dir.set(config['output_dir'])

                self.log_message("✅ 已加载上次保存的配置")
        except Exception as e:
            self.log_message(f"⚠️ 加载配置失败: {str(e)}")

    def save_config(self):
        """保存当前配置"""
        try:
            config = {
                'cookie': self.cookie_entry.get("1.0", tk.END).strip(),
                'token': self.token_entry.get().strip(),
                'search_method': self.search_method.get(),
                'nickname_or_fakeid': self.nickname_entry.get().strip(),
                'output_dir': self.output_dir.get()
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("✅ 配置已保存")
        except Exception as e:
            self.log_message(f"⚠️ 保存配置失败: {str(e)}")

    def create_widgets(self):
        """创建GUI组件"""
        # 创建主容器和滚动条
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(main_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 布局Canvas和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        self.bind_mousewheel()

        # 在可滚动框架中创建内容
        self.create_content_widgets()

    def bind_mousewheel(self):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")

        self.canvas.bind('<Enter>', _bind_to_mousewheel)
        self.canvas.bind('<Leave>', _unbind_from_mousewheel)

    def create_content_widgets(self):
        """创建主要内容组件"""
        # 标题区域
        title_frame = ttk.Frame(self.scrollable_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = ttk.Label(title_frame, text="🕷️ 微信公众号文章爬虫", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="专业的微信公众号文章采集工具", style='Subtitle.TLabel')
        subtitle_label.pack(pady=(5, 0))

        # 分隔线
        ttk.Separator(self.scrollable_frame, orient='horizontal').pack(fill=tk.X, pady=(0, 20))

        # 凭证配置区域
        self.create_credential_section()

        # 搜索配置区域
        self.create_search_section()

        # 输出配置区域
        self.create_output_section()

        # 自动化选项区域
        self.create_automation_section()

        # 控制按钮区域
        self.create_control_section()

        # 日志区域
        self.create_log_section()

    def create_credential_section(self):
        """创建凭证配置区域"""
        # 凭证配置卡片
        credential_frame = ttk.LabelFrame(self.scrollable_frame, text="🔑 凭证配置", style='Card.TLabelframe')
        credential_frame.pack(fill=tk.X, pady=(0, 15))

        # 内容容器
        content_frame = ttk.Frame(credential_frame)
        content_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(content_frame, text="Cookie:", style='Section.TLabel').pack(anchor=tk.W, pady=(0, 5))

        # Cookie输入框架
        cookie_frame = ttk.Frame(content_frame)
        cookie_frame.pack(fill=tk.X, pady=(0, 10))

        self.cookie_entry = tk.Text(cookie_frame, height=3, wrap=tk.WORD, font=('Consolas', 9))
        cookie_scrollbar = ttk.Scrollbar(cookie_frame, orient="vertical", command=self.cookie_entry.yview)
        self.cookie_entry.configure(yscrollcommand=cookie_scrollbar.set)

        self.cookie_entry.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cookie_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Token输入
        ttk.Label(content_frame, text="Token:", style='Section.TLabel').pack(anchor=tk.W, pady=(0, 5))
        self.token_entry = ttk.Entry(content_frame, font=('Consolas', 10))
        self.token_entry.pack(fill=tk.X, pady=(0, 10))

        # 凭证状态指示器
        status_frame = ttk.Frame(content_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="凭证状态:", style='Info.TLabel').pack(side=tk.LEFT)
        self.credential_status_var = tk.StringVar(value="未加载凭证")
        self.credential_status_label = ttk.Label(status_frame, textvariable=self.credential_status_var,
                                                style='Status.TLabel', foreground='gray')
        self.credential_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 凭证操作按钮
        credential_buttons = ttk.Frame(content_frame)
        credential_buttons.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(credential_buttons, text="🔄 自动获取凭证", command=self.load_from_pool,
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(credential_buttons, text="📋 选择凭证", command=self.select_credential_from_pool).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(credential_buttons, text="验证凭证", command=self.test_credentials).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(credential_buttons, text="凭证池状态", command=self.show_pool_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(credential_buttons, text="获取凭证指南", command=self.show_credential_guide).pack(side=tk.LEFT)

    def create_search_section(self):
        """创建搜索配置区域"""
        search_frame = ttk.LabelFrame(self.scrollable_frame, text="🔍 搜索配置", style='Card.TLabelframe')
        search_frame.pack(fill=tk.X, pady=(0, 15))

        content_frame = ttk.Frame(search_frame)
        content_frame.pack(fill=tk.X, padx=10, pady=10)

        # 搜索方式选择
        ttk.Label(content_frame, text="搜索方式:", style='Section.TLabel').pack(anchor=tk.W, pady=(0, 5))

        method_frame = ttk.Frame(content_frame)
        method_frame.pack(fill=tk.X, pady=(0, 10))

        self.search_method = tk.StringVar(value="nickname")
        ttk.Radiobutton(method_frame, text="公众号昵称", variable=self.search_method, value="nickname").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(method_frame, text="FakeID", variable=self.search_method, value="fakeid").pack(side=tk.LEFT)

        # 公众号昵称/FakeID输入
        ttk.Label(content_frame, text="公众号昵称/FakeID:", style='Section.TLabel').pack(anchor=tk.W, pady=(0, 5))
        self.nickname_entry = ttk.Entry(content_frame, font=('Microsoft YaHei', 10))
        self.nickname_entry.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(content_frame, text="提示：使用公众号昵称搜索更稳定，FakeID适合已知具体ID的情况",
                 style='Info.TLabel').pack(anchor=tk.W)

    def create_output_section(self):
        """创建输出配置区域"""
        output_frame = ttk.LabelFrame(self.scrollable_frame, text="📁 输出配置", style='Card.TLabelframe')
        output_frame.pack(fill=tk.X, pady=(0, 15))

        content_frame = ttk.Frame(output_frame)
        content_frame.pack(fill=tk.X, padx=10, pady=10)

        # 输出目录选择
        ttk.Label(content_frame, text="输出目录:", style='Section.TLabel').pack(anchor=tk.W, pady=(0, 5))

        dir_frame = ttk.Frame(content_frame)
        dir_frame.pack(fill=tk.X, pady=(0, 5))

        self.output_dir = tk.StringVar(value=os.path.join(os.path.expanduser("~"), "Desktop"))
        dir_entry = ttk.Entry(dir_frame, textvariable=self.output_dir, font=('Microsoft YaHei', 9))
        dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        ttk.Button(dir_frame, text="浏览", command=self.browse_output_dir).pack(side=tk.RIGHT)

        ttk.Label(content_frame, text="文件将保存为Excel格式，包含文章标题、链接、发布时间等信息",
                 style='Info.TLabel').pack(anchor=tk.W)

    def create_automation_section(self):
        """创建自动化选项区域"""
        auto_frame = ttk.LabelFrame(self.scrollable_frame, text="🤖 自动化选项", style='Card.TLabelframe')
        auto_frame.pack(fill=tk.X, pady=(0, 15))

        content_frame = ttk.Frame(auto_frame)
        content_frame.pack(fill=tk.X, padx=10, pady=10)

        self.auto_credential_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(content_frame, text="自动使用凭证池（推荐）",
                       variable=self.auto_credential_var).pack(anchor=tk.W, pady=(0, 5))
        ttk.Label(content_frame, text="启用后将自动从凭证池轮换使用凭证，避免单个凭证过度使用，提高稳定性",
                 style='Info.TLabel').pack(anchor=tk.W, padx=(20, 0))

    def create_control_section(self):
        """创建控制按钮区域"""
        control_frame = ttk.LabelFrame(self.scrollable_frame, text="🎮 操作控制", style='Card.TLabelframe')
        control_frame.pack(fill=tk.X, pady=(0, 15))

        content_frame = ttk.Frame(control_frame)
        content_frame.pack(fill=tk.X, padx=10, pady=10)

        # 主要控制按钮
        main_buttons = ttk.Frame(content_frame)
        main_buttons.pack(fill=tk.X, pady=(0, 10))

        self.start_button = ttk.Button(main_buttons, text="🚀 开始爬取", command=self.start_crawling,
                                      style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(main_buttons, text="⏹️ 停止", command=self.stop_crawling, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(main_buttons, text="🗑️ 清空日志", command=self.clear_log).pack(side=tk.LEFT)

        # 配置管理按钮
        config_buttons = ttk.Frame(content_frame)
        config_buttons.pack(fill=tk.X)

        ttk.Button(config_buttons, text="💾 保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_buttons, text="🗑️ 清除配置", command=self.clear_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_buttons, text="🔄 重新加载配置", command=self.load_config).pack(side=tk.LEFT)

    def create_log_section(self):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(self.scrollable_frame, text="📋 运行日志", style='Card.TLabelframe')
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        content_frame = ttk.Frame(log_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建日志文本框和滚动条
        self.log_text = scrolledtext.ScrolledText(content_frame, height=12, wrap=tk.WORD,
                                                 font=('Consolas', 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 添加日志级别颜色配置
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir.get())
        if directory:
            self.output_dir.set(directory)

    def log_message(self, message):
        """在日志区域显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 确定日志级别和颜色
        tag = "INFO"  # 默认
        if "✅" in message or "成功" in message:
            tag = "SUCCESS"
        elif "⚠️" in message or "警告" in message:
            tag = "WARNING"
        elif "❌" in message or "错误" in message or "失败" in message:
            tag = "ERROR"

        # 在主线程中更新GUI
        self.root.after(0, lambda: self._update_log(log_entry, tag))

    def _update_log(self, log_entry, tag="INFO"):
        """更新日志显示"""
        start_pos = self.log_text.index(tk.END)
        self.log_text.insert(tk.END, log_entry)
        end_pos = self.log_text.index(tk.END)

        # 应用颜色标签
        self.log_text.tag_add(tag, start_pos, end_pos)

        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def clear_config(self):
        """清除保存的配置"""
        try:
            # 清空界面输入
            self.cookie_entry.delete("1.0", tk.END)
            self.token_entry.delete(0, tk.END)
            self.nickname_entry.delete(0, tk.END)
            self.search_method.set("nickname")
            self.output_dir.set(os.path.join(os.path.expanduser("~"), "Desktop"))

            # 删除配置文件
            if os.path.exists(self.config_file):
                os.remove(self.config_file)

            self.log_message("✅ 配置已清除")
        except Exception as e:
            self.log_message(f"⚠️ 清除配置失败: {str(e)}")

    def test_credentials(self):
        """测试Cookie和Token是否有效"""
        cookie = self.cookie_entry.get("1.0", tk.END).strip()
        token = self.token_entry.get().strip()

        if not cookie:
            messagebox.showerror("错误", "请先输入Cookie")
            return

        if not token:
            messagebox.showerror("错误", "请先输入Token")
            return

        # 在新线程中测试凭证
        def test_thread():
            try:
                spider = WeChatSpider(cookie, token, progress_callback=self.log_message)
                is_valid, message = spider.test_credentials()

                if is_valid:
                    self.root.after(0, lambda: self.credential_status_var.set("✅ 凭证验证成功，可以开始爬取"))
                    self.root.after(0, lambda: self.credential_status_label.config(foreground='green'))
                    self.root.after(0, lambda: messagebox.showinfo("验证成功", "Cookie和Token有效，可以开始爬取！"))
                else:
                    self.root.after(0, lambda: self.credential_status_var.set(f"❌ 凭证验证失败: {message}"))
                    self.root.after(0, lambda: self.credential_status_label.config(foreground='red'))
                    self.root.after(0, lambda: messagebox.showerror("验证失败", f"验证失败：{message}\n\n请点击'获取凭证指南'查看如何获取新的Cookie和Token"))
            except Exception as e:
                self.root.after(0, lambda: self.credential_status_var.set(f"❌ 验证异常: {str(e)}"))
                self.root.after(0, lambda: self.credential_status_label.config(foreground='red'))
                self.root.after(0, lambda: messagebox.showerror("验证错误", f"验证过程中发生错误：{str(e)}"))

        thread = threading.Thread(target=test_thread)
        thread.daemon = True
        thread.start()

    def show_credential_guide(self):
        """显示获取Cookie和Token的指南"""
        guide_text = """获取微信公众号Cookie和Token的步骤：

1. 打开浏览器，访问微信公众平台：
   https://mp.weixin.qq.com/

2. 登录你的微信公众号账号

3. 登录成功后，按F12打开开发者工具

4. 切换到"Network"（网络）标签页

5. 在公众平台中进行任意操作（如点击菜单），观察网络请求

6. 找到任意一个发送到mp.weixin.qq.com的请求

7. 点击该请求，在右侧面板中：
   - 找到"Request Headers"部分
   - 复制"Cookie"字段的完整内容
   - 在URL参数中找到"token"参数的值

8. 将Cookie和Token分别粘贴到本工具的对应输入框中

注意事项：
- Cookie和Token会定期过期，需要重新获取
- 不要在多个地方同时使用同一个账号，可能导致登录冲突
- 建议定期更新凭证以确保正常使用

如果仍然无法获取文章，可能的原因：
1. 凭证已过期，需要重新获取
2. 账号权限不足
3. 微信平台临时限制
4. 网络连接问题

建议先点击"验证凭证"按钮测试凭证是否有效。"""

        # 创建新窗口显示指南
        guide_window = tk.Toplevel(self.root)
        guide_window.title("获取Cookie和Token指南")
        guide_window.geometry("600x500")
        guide_window.resizable(True, True)

        # 创建滚动文本框
        text_frame = ttk.Frame(guide_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)

        text_widget = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, width=70, height=30)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, guide_text)
        text_widget.config(state=tk.DISABLED)  # 设置为只读

        # 关闭按钮
        ttk.Button(guide_window, text="关闭", command=guide_window.destroy).pack(pady=10)

    def load_credential_pool(self):
        """从凭证池加载凭证"""
        try:
            import json
            import os

            pool_file = "credential_pool.json"
            if not os.path.exists(pool_file):
                return None

            with open(pool_file, 'r', encoding='utf-8') as f:
                pool_data = json.load(f)

            credentials = pool_data.get('credentials', [])
            if not credentials:
                return None

            # 过滤有效凭证
            valid_credentials = [cred for cred in credentials if not cred.get('invalid', False)]
            if not valid_credentials:
                return None

            # 获取当前索引
            current_index = pool_data.get('current_index', 0)
            if current_index >= len(valid_credentials):
                current_index = 0

            # 选择当前凭证
            current_cred = valid_credentials[current_index]

            # 更新使用统计
            current_cred['use_count'] = current_cred.get('use_count', 0) + 1
            current_cred['last_used'] = datetime.now().isoformat()

            # 轮换到下一个凭证
            next_index = (current_index + 1) % len(valid_credentials)
            pool_data['current_index'] = next_index

            # 保存更新后的凭证池
            with open(pool_file, 'w', encoding='utf-8') as f:
                json.dump(pool_data, f, indent=2, ensure_ascii=False)

            self.log_message(f"🔄 从凭证池获取凭证: {current_cred.get('name', '未命名')}")
            self.log_message(f"📊 使用次数: {current_cred['use_count']}, 下次将使用: {valid_credentials[next_index].get('name', '未命名')}")

            return current_cred

        except Exception as e:
            self.log_message(f"❌ 加载凭证池失败: {str(e)}")
            return None

    def show_pool_status(self):
        """显示凭证池状态"""
        try:
            import json
            import os

            pool_file = "credential_pool.json"
            if not os.path.exists(pool_file):
                messagebox.showinfo("凭证池状态", "凭证池为空\n\n建议：\n1. 使用浏览器扩展自动提取凭证\n2. 或手动添加凭证到凭证池")
                return

            with open(pool_file, 'r', encoding='utf-8') as f:
                pool_data = json.load(f)

            credentials = pool_data.get('credentials', [])
            if not credentials:
                messagebox.showinfo("凭证池状态", "凭证池为空")
                return

            # 统计信息
            total_count = len(credentials)
            valid_count = len([c for c in credentials if not c.get('invalid', False)])
            auto_count = len([c for c in credentials if c.get('auto_extracted', False)])

            current_index = pool_data.get('current_index', 0)
            current_name = "无" if current_index >= len(credentials) else credentials[current_index].get('name', '未命名')

            status_text = f"""凭证池状态报告

📊 统计信息:
• 总凭证数: {total_count}
• 有效凭证: {valid_count}
• 自动提取: {auto_count}
• 手动添加: {total_count - auto_count}

🔄 当前状态:
• 当前使用: {current_name}
• 轮换索引: {current_index + 1}/{total_count}

💡 建议:
• 保持3-5个有效凭证以获得最佳效果
• 定期使用浏览器扩展更新凭证
• 启用自动轮换以延长凭证有效期"""

            messagebox.showinfo("凭证池状态", status_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取凭证池状态失败: {str(e)}")

    def load_from_pool(self):
        """从凭证池加载凭证到界面"""
        try:
            # 加载凭证
            pool_credential = self.load_credential_pool()

            if not pool_credential:
                # 如果凭证池为空，显示详细信息
                messagebox.showinfo("凭证池为空",
                                  "凭证池中没有可用凭证\n\n获取凭证的方法：\n\n1. 使用浏览器扩展自动提取：\n   • 点击'生成扩展'按钮\n   • 安装Chrome扩展\n   • 在微信公众平台一键提取\n\n2. 手动添加到凭证池：\n   • 手动获取Cookie和Token\n   • 使用凭证池管理工具添加\n\n3. 直接在此界面输入：\n   • 手动填写Cookie和Token输入框")
                return

            # 清空现有内容
            self.cookie_entry.delete("1.0", tk.END)
            self.token_entry.delete(0, tk.END)

            # 填充新凭证
            self.cookie_entry.insert("1.0", pool_credential['cookie'])
            self.token_entry.insert(0, pool_credential['token'])

            # 更新凭证状态显示
            cred_name = pool_credential.get('name', '未命名')
            use_count = pool_credential.get('use_count', 0)
            source = pool_credential.get('source', 'unknown')

            if source == 'browser_extension':
                source_text = '浏览器扩展'
            elif source == 'manual':
                source_text = '手动添加'
            else:
                source_text = source

            status_text = f"✅ {cred_name} (来源: {source_text}, 使用: {use_count}次)"
            self.credential_status_var.set(status_text)
            self.credential_status_label.config(foreground='green')

            success_msg = f"✅ 已从凭证池加载凭证\n\n凭证信息：\n• 名称: {cred_name}\n• 来源: {source_text}\n• 使用次数: {use_count}\n\n建议：\n• 点击'验证凭证'确保有效性\n• 启用'自动使用凭证池'获得最佳体验"

            self.log_message(f"✅ 已从凭证池加载凭证: {cred_name}")
            messagebox.showinfo("凭证加载成功", success_msg)

        except Exception as e:
            error_msg = f"从凭证池加载凭证失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("加载失败", f"{error_msg}\n\n请检查：\n1. 凭证池文件是否存在\n2. 凭证池中是否有有效凭证\n3. 文件权限是否正确")

    def select_credential_from_pool(self):
        """从凭证池选择特定凭证"""
        try:
            import json
            import os

            pool_file = "credential_pool.json"
            if not os.path.exists(pool_file):
                messagebox.showinfo("凭证池为空", "凭证池文件不存在，请先添加凭证")
                return

            with open(pool_file, 'r', encoding='utf-8') as f:
                pool_data = json.load(f)

            credentials = pool_data.get('credentials', [])
            if not credentials:
                messagebox.showinfo("凭证池为空", "凭证池中没有凭证")
                return

            # 创建选择对话框
            select_window = tk.Toplevel(self.root)
            select_window.title("选择凭证")
            select_window.geometry("600x400")
            select_window.transient(self.root)
            select_window.grab_set()

            # 主框架
            main_frame = ttk.Frame(select_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            ttk.Label(main_frame, text="选择要使用的凭证",
                     font=('Microsoft YaHei', 12, 'bold')).pack(pady=(0, 15))

            # 凭证列表
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # 创建列表框
            columns = ('名称', '来源', '状态', '使用次数', '最后使用')
            tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

            # 设置列标题和宽度
            tree.heading('名称', text='名称')
            tree.heading('来源', text='来源')
            tree.heading('状态', text='状态')
            tree.heading('使用次数', text='使用次数')
            tree.heading('最后使用', text='最后使用')

            tree.column('名称', width=150)
            tree.column('来源', width=100)
            tree.column('状态', width=80)
            tree.column('使用次数', width=80)
            tree.column('最后使用', width=120)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 填充数据
            for i, cred in enumerate(credentials):
                status = "❌ 失效" if cred.get('invalid', False) else "✅ 有效"
                source = cred.get('source', 'manual')
                if source == 'browser_extension':
                    source = '浏览器扩展'
                elif source == 'manual':
                    source = '手动添加'

                last_used = cred.get('last_used', '从未')
                if last_used != '从未' and last_used:
                    try:
                        last_used = datetime.fromisoformat(last_used).strftime("%m-%d %H:%M")
                    except:
                        pass

                tree.insert('', tk.END, values=(
                    cred.get('name', f'凭证{i+1}'),
                    source,
                    status,
                    cred.get('use_count', 0),
                    last_used
                ), tags=(str(i),))

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            def use_selected():
                selection = tree.selection()
                if not selection:
                    messagebox.showwarning("未选择", "请先选择一个凭证")
                    return

                # 获取选中的凭证索引
                item = tree.item(selection[0])
                cred_index = int(item['tags'][0])
                selected_cred = credentials[cred_index]

                if selected_cred.get('invalid', False):
                    if not messagebox.askyesno("凭证可能无效",
                                             f"凭证 '{selected_cred.get('name')}' 标记为无效\n\n是否仍要使用？"):
                        return

                # 应用选中的凭证
                self.cookie_entry.delete("1.0", tk.END)
                self.token_entry.delete(0, tk.END)
                self.cookie_entry.insert("1.0", selected_cred['cookie'])
                self.token_entry.insert(0, selected_cred['token'])

                # 更新使用统计
                selected_cred['use_count'] = selected_cred.get('use_count', 0) + 1
                selected_cred['last_used'] = datetime.now().isoformat()

                # 更新凭证状态显示
                cred_name = selected_cred.get('name', '未命名')
                use_count = selected_cred['use_count']
                source = selected_cred.get('source', 'unknown')

                if source == 'browser_extension':
                    source_text = '浏览器扩展'
                elif source == 'manual':
                    source_text = '手动添加'
                else:
                    source_text = source

                status_text = f"✅ {cred_name} (来源: {source_text}, 使用: {use_count}次)"
                self.credential_status_var.set(status_text)
                self.credential_status_label.config(foreground='green')

                # 保存更新
                with open(pool_file, 'w', encoding='utf-8') as f:
                    json.dump(pool_data, f, indent=2, ensure_ascii=False)

                self.log_message(f"✅ 已应用凭证: {cred_name}")
                select_window.destroy()
                messagebox.showinfo("应用成功", f"已应用凭证: {cred_name}")

            ttk.Button(button_frame, text="使用选中凭证", command=use_selected).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="取消", command=select_window.destroy).pack(side=tk.LEFT)

        except Exception as e:
            messagebox.showerror("错误", f"选择凭证失败: {str(e)}")

    def validate_inputs(self):
        """验证输入"""
        cookie = self.cookie_entry.get("1.0", tk.END).strip()
        token = self.token_entry.get().strip()
        nickname_or_fakeid = self.nickname_entry.get().strip()

        if not cookie:
            messagebox.showerror("错误", "请输入Cookie")
            return False

        if not token:
            messagebox.showerror("错误", "请输入Token")
            return False

        if not nickname_or_fakeid:
            messagebox.showerror("错误", "请输入公众号昵称或FakeID")
            return False

        return True

    def start_crawling(self):
        """开始爬取"""
        # 检查是否启用自动凭证池
        if self.auto_credential_var.get():
            self.log_message("🤖 启用自动凭证池模式")
            # 从凭证池加载凭证
            pool_credential = self.load_credential_pool()
            if pool_credential:
                # 自动填充凭证
                self.cookie_entry.delete("1.0", tk.END)
                self.cookie_entry.insert("1.0", pool_credential['cookie'])
                self.token_entry.delete(0, tk.END)
                self.token_entry.insert(0, pool_credential['token'])
                self.log_message(f"✅ 已自动加载凭证: {pool_credential.get('name', '未命名')}")
            else:
                self.log_message("⚠️ 凭证池为空，使用手动输入的凭证")
                if not self.validate_inputs():
                    messagebox.showwarning("凭证池为空",
                                         "凭证池中没有可用凭证，请：\n\n1. 使用浏览器扩展自动提取凭证\n2. 或手动输入Cookie和Token\n3. 或添加凭证到凭证池")
                    return
        else:
            if not self.validate_inputs():
                return

        # 自动保存配置
        self.save_config()

        # 在开始爬取前先验证凭证
        self.log_message("🔍 开始前验证凭证...")
        cookie = self.cookie_entry.get("1.0", tk.END).strip()
        token = self.token_entry.get().strip()

        try:
            spider = WeChatSpider(cookie, token, progress_callback=self.log_message)
            is_valid, message = spider.test_credentials()

            if not is_valid:
                self.log_message(f"❌ 凭证验证失败: {message}")

                # 如果启用了自动凭证池，尝试切换到下一个凭证
                if self.auto_credential_var.get():
                    self.log_message("🔄 尝试切换到下一个凭证...")
                    next_credential = self.load_credential_pool()
                    if next_credential and next_credential['token'] != token:
                        self.cookie_entry.delete("1.0", tk.END)
                        self.cookie_entry.insert("1.0", next_credential['cookie'])
                        self.token_entry.delete(0, tk.END)
                        self.token_entry.insert(0, next_credential['token'])

                        # 重新验证
                        spider = WeChatSpider(next_credential['cookie'], next_credential['token'], progress_callback=self.log_message)
                        is_valid, message = spider.test_credentials()

                        if is_valid:
                            self.log_message(f"✅ 切换凭证成功: {next_credential.get('name', '未命名')}")
                        else:
                            self.log_message("❌ 切换凭证仍然无效")
                            messagebox.showerror("凭证无效", "凭证池中的凭证都已失效\n\n请使用浏览器扩展获取新的有效凭证")
                            return
                    else:
                        messagebox.showerror("凭证无效", f"凭证验证失败：{message}\n\n请重新获取Cookie和Token后再试")
                        return
                else:
                    messagebox.showerror("凭证无效", f"凭证验证失败：{message}\n\n请重新获取Cookie和Token后再试")
                    return
            else:
                self.log_message("✅ 凭证验证通过，开始爬取")
        except Exception as e:
            self.log_message(f"❌ 凭证验证异常: {str(e)}")
            messagebox.showerror("验证异常", f"凭证验证过程中发生异常：{str(e)}")
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 在新线程中运行爬取任务
        thread = threading.Thread(target=self.crawl_articles)
        thread.daemon = True
        thread.start()

    def stop_crawling(self):
        """停止爬取"""
        self.is_running = False
        self.log_message("⏹️ 正在停止爬取任务，请稍候...")
        # 注意：按钮状态会在爬取线程结束时恢复

    def create_cache_file(self, name):
        """创建缓存文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            cache_filename = f"{name}_cache_{timestamp}.json"
            cache_filepath = os.path.join(self.output_dir.get(), cache_filename)

            # 创建空的缓存文件
            cache_data = {
                "session_id": timestamp,
                "target_name": name,
                "articles": [],
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            }

            with open(cache_filepath, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

            self.cache_file = cache_filepath
            self.log_message(f"📝 创建缓存文件: {cache_filename}")
            return cache_filepath

        except Exception as e:
            self.log_message(f"❌ 创建缓存文件失败: {str(e)}")
            return None

    def update_cache_file(self, articles):
        """更新缓存文件"""
        if not self.cache_file or not os.path.exists(self.cache_file):
            return False

        try:
            # 读取现有缓存
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 更新数据
            cache_data["articles"] = articles
            cache_data["last_updated"] = datetime.now().isoformat()
            cache_data["article_count"] = len(articles)

            # 写回缓存文件
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

            return True

        except Exception as e:
            self.log_message(f"❌ 更新缓存文件失败: {str(e)}")
            return False

    def cleanup_previous_files(self):
        """清理上一个保存的文件（如果新文件保存成功）"""
        if self.previous_save_file and os.path.exists(self.previous_save_file):
            try:
                os.remove(self.previous_save_file)
                self.log_message(f"🗑️ 已删除上一个文件: {os.path.basename(self.previous_save_file)}")
                return True
            except Exception as e:
                self.log_message(f"⚠️ 删除上一个文件失败: {str(e)}")
                return False
        return True

    def auto_save_articles(self, articles, save_type):
        """自动保存文章的回调函数"""
        try:
            nickname_or_fakeid = self.nickname_entry.get().strip()

            # 首先更新缓存文件
            if self.cache_file:
                self.update_cache_file(articles)
            else:
                # 如果没有缓存文件，创建一个
                self.create_cache_file(nickname_or_fakeid)
                self.update_cache_file(articles)

            # 尝试保存Excel文件
            success = self.save_to_excel(articles, nickname_or_fakeid, auto_save=True, save_type=save_type)

            # 如果保存成功，清理上一个文件
            if success and save_type.startswith("auto_save"):
                self.cleanup_previous_files()

        except Exception as e:
            self.log_message(f"自动保存失败: {str(e)}")

    def handle_credential_expired(self):
        """处理凭证过期，尝试自动切换"""
        if not self.auto_credential_var.get():
            return False

        self.log_message("🔄 检测到凭证过期，尝试自动切换...")

        try:
            # 从凭证池获取下一个凭证
            next_credential = self.load_credential_pool()
            if not next_credential:
                self.log_message("❌ 凭证池中没有可用凭证")
                return False

            # 验证新凭证
            spider = WeChatSpider(next_credential['cookie'], next_credential['token'], progress_callback=self.log_message)
            is_valid, message = spider.test_credentials()

            if is_valid:
                # 更新当前使用的凭证
                self.spider = spider
                self.log_message(f"✅ 自动切换凭证成功: {next_credential.get('name', '未命名')}")
                return True
            else:
                self.log_message(f"❌ 新凭证也已失效: {message}")
                return False

        except Exception as e:
            self.log_message(f"❌ 自动切换凭证失败: {str(e)}")
            return False

    def crawl_articles(self):
        """爬取文章的主要逻辑"""
        articles = []
        nickname_or_fakeid = ""

        try:
            cookie = self.cookie_entry.get("1.0", tk.END).strip()
            token = self.token_entry.get().strip()
            nickname_or_fakeid = self.nickname_entry.get().strip()
            search_method = self.search_method.get()

            self.log_message(f"开始爬取公众号: {nickname_or_fakeid}")
            self.log_message(f"搜索方式: {'昵称搜索' if search_method == 'nickname' else 'FakeID直接获取'}")

            # 创建缓存文件
            self.create_cache_file(nickname_or_fakeid)

            # 创建爬虫实例
            self.spider = WeChatSpider(cookie, token, progress_callback=self.log_message)

            # 定义停止检查函数
            def should_stop():
                return not self.is_running

            # 根据搜索方式获取文章，支持自动凭证切换
            max_retries = 3 if self.auto_credential_var.get() else 1

            for retry in range(max_retries):
                try:
                    if search_method == "nickname":
                        articles = self.spider.fetch_articles_by_nickname(
                            nickname_or_fakeid,
                            stop_flag=should_stop,
                            auto_save_callback=self.auto_save_articles
                        )
                    else:
                        articles = self.spider.fetch_articles_by_fakeid(
                            nickname_or_fakeid,
                            stop_flag=should_stop,
                            auto_save_callback=self.auto_save_articles
                        )
                    break  # 成功则跳出重试循环

                except Exception as e:
                    if "Cookie/Token 已过期" in str(e) and retry < max_retries - 1:
                        self.log_message(f"🔄 第{retry + 1}次重试：检测到凭证过期")
                        if self.handle_credential_expired():
                            self.log_message("✅ 凭证切换成功，继续爬取...")
                            continue
                        else:
                            self.log_message("❌ 无法切换到有效凭证")
                            raise e
                    else:
                        raise e

            # 检查是否被用户停止
            if not self.is_running:
                if articles:
                    self.log_message(f"⏹️ 爬取已停止，已获取 {len(articles)} 篇文章，正在保存...")
                    self.save_to_excel(articles, nickname_or_fakeid, stopped=True)
                else:
                    self.log_message("⏹️ 爬取已停止，未获取到文章")
                return

            if not articles:
                self.log_message(f"未获取到任何文章")
                return

            # 保存到Excel
            self.save_to_excel(articles, nickname_or_fakeid)

        except Exception as e:
            error_str = str(e)
            self.log_message(f"发生错误: {error_str}")

            # 如果有部分文章，尝试保存
            if articles:
                try:
                    self.log_message(f"尝试保存已获取的 {len(articles)} 篇文章...")
                    self.save_to_excel(articles, nickname_or_fakeid, error=True)
                except:
                    pass

            # 根据错误类型提供不同的处理建议
            if "Cookie/Token 已过期" in error_str:
                error_msg = f"凭证已过期！\n\n错误详情：{error_str}\n\n解决方案：\n1. 点击'获取凭证指南'查看获取步骤\n2. 重新获取Cookie和Token\n3. 点击'验证凭证'确保有效\n4. 重新开始爬取"
                self.root.after(0, lambda: messagebox.showerror("凭证过期", error_msg))
            elif "系统繁忙" in error_str:
                error_msg = f"微信系统繁忙！\n\n错误详情：{error_str}\n\n建议：\n1. 等待几分钟后重试\n2. 避免频繁请求\n3. 检查网络连接"
                self.root.after(0, lambda: messagebox.showwarning("系统繁忙", error_msg))
            elif "参数错误" in error_str or "FakeID" in error_str:
                error_msg = f"参数错误！\n\n错误详情：{error_str}\n\n建议：\n1. 检查公众号昵称是否正确\n2. 如果使用FakeID，请确认格式正确\n3. 尝试使用公众号昵称搜索"
                self.root.after(0, lambda: messagebox.showerror("参数错误", error_msg))
            elif "网络" in error_str:
                error_msg = f"网络连接问题！\n\n错误详情：{error_str}\n\n建议：\n1. 检查网络连接\n2. 尝试重新连接网络\n3. 稍后重试"
                self.root.after(0, lambda: messagebox.showerror("网络错误", error_msg))
            else:
                error_msg = f"爬取失败！\n\n错误详情：{error_str}\n\n建议：\n1. 检查Cookie和Token是否有效\n2. 确认公众号信息正确\n3. 查看详细日志了解具体问题"
                self.root.after(0, lambda: messagebox.showerror("爬取失败", error_msg))

        finally:
            # 如果有缓存文件且爬取异常结束，从缓存文件恢复数据
            if self.cache_file and os.path.exists(self.cache_file) and not articles:
                try:
                    with open(self.cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)

                    cached_articles = cache_data.get('articles', [])
                    if cached_articles:
                        self.log_message(f"📋 从缓存文件恢复 {len(cached_articles)} 篇文章数据")
                        # 保存缓存的文章数据
                        self.save_to_excel(cached_articles, nickname_or_fakeid, error=True)

                except Exception as e:
                    self.log_message(f"❌ 从缓存恢复数据失败: {str(e)}")

            # 恢复按钮状态
            self.root.after(0, lambda: self.start_button.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_button.config(state=tk.DISABLED))
            self.is_running = False

    def save_to_excel(self, articles, name, stopped=False, error=False, auto_save=False, save_type=""):
        """保存文章到Excel文件"""
        try:
            df = pd.DataFrame(articles)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            status_suffix = ""
            if auto_save:
                status_suffix = f"_{save_type}"
            elif stopped:
                status_suffix = "_stopped"
            elif error:
                status_suffix = "_partial"

            filename = f"{name}_articles_{timestamp}{status_suffix}.xlsx"
            filepath = os.path.join(self.output_dir.get(), filename)

            # 保存到Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='文章列表')

            # 记录当前保存的文件
            if auto_save:
                # 对于自动保存，记录上一个文件以便后续删除
                if self.previous_save_file != filepath:  # 避免重复记录同一个文件
                    self.previous_save_file = filepath
                self.current_session_files.append(filepath)
            else:
                # 对于最终保存，清理缓存文件
                if self.cache_file and os.path.exists(self.cache_file):
                    try:
                        os.remove(self.cache_file)
                        self.log_message(f"🗑️ 已删除缓存文件: {os.path.basename(self.cache_file)}")
                        self.cache_file = None
                    except Exception as e:
                        self.log_message(f"⚠️ 删除缓存文件失败: {str(e)}")

            # 根据状态显示不同的消息
            if auto_save:
                message = f"💾 自动保存 {len(articles)} 篇文章到: {os.path.basename(filepath)}"
                self.log_message(message)
                # 自动保存不显示弹窗，只记录日志
            elif stopped:
                message = f"⏹️ 爬取已停止，成功保存 {len(articles)} 篇文章到: {filepath}"
                self.log_message(message)
                messagebox.showinfo("停止完成", f"爬取已停止\n成功保存 {len(articles)} 篇文章到:\n{filepath}")
            elif error:
                message = f"⚠️ 发生错误，已保存 {len(articles)} 篇文章到: {filepath}"
                self.log_message(message)
                messagebox.showwarning("部分保存", f"发生错误，但已保存 {len(articles)} 篇文章到:\n{filepath}")
            else:
                message = f"✅ 成功保存 {len(articles)} 篇文章到: {filepath}"
                self.log_message(message)
                messagebox.showinfo("完成", f"爬取完成！\n成功保存 {len(articles)} 篇文章到:\n{filepath}")

            return True  # 保存成功

        except Exception as e:
            error_msg = f"保存Excel文件失败: {str(e)}"
            self.log_message(error_msg)
            if not auto_save:  # 只有非自动保存时才显示错误弹窗
                messagebox.showerror("错误", error_msg)
            return False  # 保存失败


def main():
    """主函数"""
    root = tk.Tk()
    app = WeChatSpiderGUI(root)
    
    # 添加关闭事件处理
    def on_closing():
        if app.is_running:
            if messagebox.askokcancel("退出", "爬取任务正在运行，确定要退出吗？"):
                app.stop_crawling()
                app.save_config()  # 保存配置
                root.destroy()
        else:
            app.save_config()  # 保存配置
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
