#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
浏览器扩展助手
生成浏览器扩展，自动提取微信公众平台凭证
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import os
import json
import zipfile
from datetime import datetime

class BrowserExtensionHelper:
    def __init__(self, root):
        self.root = root
        self.root.title("浏览器扩展助手")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🧩 浏览器扩展助手", 
                               font=('Microsoft YaHei', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_text = """这个工具会生成一个Chrome浏览器扩展，可以：
• 自动检测微信公众平台登录状态
• 一键提取Cookie和Token
• 安全传输到本地应用
• 支持自动更新凭证池"""
        
        info_label = ttk.Label(main_frame, text=info_text, 
                              font=('Microsoft YaHei', 9), foreground='gray')
        info_label.pack(pady=(0, 20))
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(button_frame, text="生成浏览器扩展", 
                  command=self.generate_extension).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="安装说明", 
                  command=self.show_install_guide).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="启动本地服务", 
                  command=self.start_local_server).pack(side=tk.LEFT)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.pack(pady=(0, 15))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def generate_extension(self):
        """生成浏览器扩展"""
        try:
            # 选择保存位置
            save_path = filedialog.askdirectory(title="选择扩展保存位置")
            if not save_path:
                return
                
            extension_dir = os.path.join(save_path, "wechat_credential_extractor")
            os.makedirs(extension_dir, exist_ok=True)
            
            self.log_message("正在生成浏览器扩展...")
            
            # 生成manifest.json
            self.create_manifest(extension_dir)
            
            # 生成content script
            self.create_content_script(extension_dir)
            
            # 生成background script
            self.create_background_script(extension_dir)
            
            # 生成popup页面
            self.create_popup(extension_dir)
            
            # 生成图标
            self.create_icons(extension_dir)
            
            # 打包为zip文件
            zip_path = os.path.join(save_path, "wechat_credential_extractor.zip")
            self.create_zip(extension_dir, zip_path)
            
            self.log_message(f"✅ 浏览器扩展生成完成")
            self.log_message(f"扩展目录: {extension_dir}")
            self.log_message(f"打包文件: {zip_path}")
            
            messagebox.showinfo("生成完成", 
                               f"浏览器扩展已生成完成！\n\n扩展目录: {extension_dir}\n打包文件: {zip_path}\n\n点击'安装说明'查看如何安装")
            
        except Exception as e:
            self.log_message(f"❌ 生成扩展失败: {str(e)}")
            messagebox.showerror("生成失败", f"生成扩展失败: {str(e)}")
            
    def create_manifest(self, extension_dir):
        """创建manifest.json"""
        manifest = {
            "manifest_version": 3,
            "name": "微信公众号凭证提取器",
            "version": "2.0",
            "description": "自动提取微信公众平台的完整Cookie和Token",
            "permissions": [
                "activeTab",
                "storage",
                "tabs",
                "cookies",
                "webRequest"
            ],
            "host_permissions": [
                "https://mp.weixin.qq.com/*"
            ],
            "content_scripts": [
                {
                    "matches": ["https://mp.weixin.qq.com/*"],
                    "js": ["content.js"],
                    "run_at": "document_end"
                }
            ],
            "background": {
                "service_worker": "background.js"
            },
            "action": {
                "default_popup": "popup.html",
                "default_title": "微信凭证提取器"
            },
            "icons": {
                "16": "icon16.png",
                "48": "icon48.png",
                "128": "icon128.png"
            }
        }
        
        with open(os.path.join(extension_dir, "manifest.json"), 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
            
    def create_content_script(self, extension_dir):
        """创建content script"""
        content_js = """
// 微信公众号凭证提取器 - Content Script v3.0
console.log('微信凭证提取器已加载 - 网络请求监听版');

// 提取完整Cookie（优先使用网络请求捕获的）
async function extractCompleteCookie() {
    try {
        // 方法1：获取从网络请求中捕获的Cookie
        const networkCredentials = await chrome.runtime.sendMessage({action: 'getLatestCredentials'});
        if (networkCredentials && networkCredentials.available && networkCredentials.cookie.length > 800) {
            console.log('使用网络请求捕获的Cookie，长度:', networkCredentials.cookie.length);
            return networkCredentials.cookie;
        }

        // 方法2：通过Chrome Cookie API获取
        const apiResult = await chrome.runtime.sendMessage({action: 'getCookies'});
        if (apiResult && apiResult.cookie) {
            console.log('使用Cookie API获取，长度:', apiResult.cookie.length);
            return apiResult.cookie;
        }
    } catch (error) {
        console.log('获取增强Cookie失败，使用备用方法:', error);
    }

    // 方法3：备用方法 - 使用document.cookie
    console.log('使用document.cookie备用方法');
    return document.cookie;
}

// 提取Token（优先使用网络请求捕获的）
async function extractCompleteToken() {
    try {
        // 方法1：获取从网络请求中捕获的Token
        const networkCredentials = await chrome.runtime.sendMessage({action: 'getLatestCredentials'});
        if (networkCredentials && networkCredentials.available && networkCredentials.token) {
            console.log('使用网络请求捕获的Token:', networkCredentials.token);
            return networkCredentials.token;
        }
    } catch (error) {
        console.log('获取网络Token失败，使用备用方法:', error);
    }

    // 方法2：从URL提取Token
    const url = window.location.href;
    const tokenMatch = url.match(/token=([^&]+)/);
    return tokenMatch ? tokenMatch[1] : '';
}

// 提取Cookie（兼容旧版本）
function extractCookie() {
    return document.cookie;
}

// 提取Token（兼容旧版本）
function extractToken() {
    const url = window.location.href;
    const tokenMatch = url.match(/token=([^&]+)/);
    return tokenMatch ? tokenMatch[1] : '';
}

// 检查是否在微信公众平台
function isWeChatPlatform() {
    return window.location.hostname === 'mp.weixin.qq.com';
}

// 检查是否已登录
function isLoggedIn() {
    return window.location.href.includes('token=') || 
           document.querySelector('.weui-desktop-account') !== null;
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractCredentials') {
        if (!isWeChatPlatform()) {
            sendResponse({
                success: false,
                error: '请在微信公众平台页面使用此功能'
            });
            return;
        }
        
        if (!isLoggedIn()) {
            sendResponse({
                success: false,
                error: '请先登录微信公众平台'
            });
            return;
        }
        
        // 使用增强的Cookie和Token提取方法
        const cookie = await extractCompleteCookie();
        const token = await extractCompleteToken();

        if (!cookie || !token) {
            sendResponse({
                success: false,
                error: '无法提取完整的凭证信息'
            });
            return;
        }

        sendResponse({
            success: true,
            data: {
                cookie: cookie,
                token: token,
                timestamp: new Date().toISOString(),
                url: window.location.href
            }
        });
    }
});

// 自动检测登录状态变化
let lastLoginStatus = isLoggedIn();
setInterval(() => {
    const currentLoginStatus = isLoggedIn();
    if (currentLoginStatus !== lastLoginStatus) {
        lastLoginStatus = currentLoginStatus;
        
        if (currentLoginStatus) {
            // 登录成功，自动提取凭证
            Promise.all([extractCompleteCookie(), extractCompleteToken()]).then(([cookie, token]) => {
                if (cookie && token) {
                    chrome.runtime.sendMessage({
                        action: 'credentialsDetected',
                        data: {
                            cookie: cookie,
                            token: token,
                            timestamp: new Date().toISOString(),
                            url: window.location.href,
                            source: 'auto_detection'
                        }
                    });
                }
            });
        }
    }
}, 2000);
"""
        
        with open(os.path.join(extension_dir, "content.js"), 'w', encoding='utf-8') as f:
            f.write(content_js)
            
    def create_background_script(self, extension_dir):
        """创建background script"""
        background_js = """
// 微信公众号凭证提取器 - Background Script v3.0
// 监听网络请求以获取完整的Cookie

let latestRequestCookie = null;
let latestToken = null;

// 监听网络请求，专门捕获搜索公众号时的请求标头Cookie
chrome.webRequest.onBeforeSendHeaders.addListener(
    function(details) {
        // 检查是否为搜索公众号的请求
        const isSearchRequest = details.url.includes('/cgi-bin/searchbiz') &&
                               details.url.includes('action=search_biz');

        if (details.url.includes('mp.weixin.qq.com')) {
            // 查找Cookie和Token
            for (let header of details.requestHeaders) {
                if (header.name.toLowerCase() === 'cookie') {
                    latestRequestCookie = header.value;

                    if (isSearchRequest) {
                        console.log('🎯 捕获到搜索请求的Cookie！长度:', header.value.length);
                        // 提取搜索的公众号昵称
                        const queryMatch = details.url.match(/query=([^&]+)/);
                        const searchQuery = queryMatch ? decodeURIComponent(queryMatch[1]) : '未知';
                        console.log('搜索的公众号:', searchQuery);
                    } else {
                        console.log('捕获到请求标头Cookie，长度:', header.value.length);
                    }
                }
            }

            // 从URL中提取Token
            const tokenMatch = details.url.match(/token=([^&]+)/);
            if (tokenMatch) {
                latestToken = tokenMatch[1];
                if (isSearchRequest) {
                    console.log('🎯 搜索请求Token:', latestToken);
                } else {
                    console.log('捕获到Token:', latestToken);
                }
            }

            // 如果同时获取到Cookie和Token，自动保存
            if (latestRequestCookie && latestToken && latestRequestCookie.length > 800) {
                const queryMatch = details.url.match(/query=([^&]+)/);
                const searchQuery = queryMatch ? decodeURIComponent(queryMatch[1]) : null;

                const credentials = {
                    cookie: latestRequestCookie,
                    token: latestToken,
                    timestamp: new Date().toISOString(),
                    url: details.url,
                    source: isSearchRequest ? 'search_request' : 'network_request',
                    searchQuery: searchQuery,
                    requestType: isSearchRequest ? '搜索公众号请求' : '普通请求'
                };

                // 保存到storage
                chrome.storage.local.set({
                    'latestCredentials': credentials
                });

                // 发送到本地服务器
                sendToLocalServer(credentials);

                // 显示通知
                if (isSearchRequest) {
                    chrome.action.setBadgeText({text: '🎯'});
                    chrome.action.setBadgeBackgroundColor({color: '#FF5722'});
                    console.log('🎯 自动保存搜索请求凭证，Cookie长度:', latestRequestCookie.length, '搜索:', searchQuery);
                } else {
                    chrome.action.setBadgeText({text: '✓'});
                    chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
                    console.log('自动保存完整凭证，Cookie长度:', latestRequestCookie.length);
                }
            }
        }
    },
    {urls: ["https://mp.weixin.qq.com/*"]},
    ["requestHeaders"]
);

// 监听来自content script的消息
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
    if (request.action === 'credentialsDetected') {
        // 保存凭证到storage
        chrome.storage.local.set({
            'latestCredentials': request.data
        });

        // 发送到本地服务器（如果可用）
        sendToLocalServer(request.data);

        // 显示通知
        chrome.action.setBadgeText({text: '!'});
        chrome.action.setBadgeBackgroundColor({color: '#4CAF50'});
    } else if (request.action === 'getCookies') {
        // 优先返回从网络请求中捕获的Cookie
        if (latestRequestCookie && latestToken) {
            sendResponse({
                cookie: latestRequestCookie,
                token: latestToken,
                source: 'network_request'
            });
            return true;
        }

        // 备用方案：通过Chrome Cookie API获取
        try {
            const cookies = await chrome.cookies.getAll({
                domain: '.weixin.qq.com'
            });
            const mpCookies = await chrome.cookies.getAll({
                domain: 'mp.weixin.qq.com'
            });

            const allCookies = [...cookies, ...mpCookies];
            const cookieString = allCookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

            sendResponse({
                cookie: cookieString,
                token: latestToken || '',
                source: 'cookie_api'
            });
        } catch (error) {
            console.error('获取Cookie失败:', error);
            sendResponse({cookie: '', token: '', source: 'error'});
        }
        return true;
    } else if (request.action === 'getLatestCredentials') {
        // 返回最新捕获的凭证
        sendResponse({
            cookie: latestRequestCookie || '',
            token: latestToken || '',
            available: !!(latestRequestCookie && latestToken)
        });
        return true;
    }
});

// 发送凭证到本地服务器
async function sendToLocalServer(credentials) {
    try {
        const response = await fetch('http://localhost:8888/credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials)
        });
        
        if (response.ok) {
            console.log('凭证已发送到本地服务器');
        }
    } catch (error) {
        console.log('本地服务器不可用:', error);
    }
}

// 清除徽章
chrome.action.onClicked.addListener(() => {
    chrome.action.setBadgeText({text: ''});
});
"""
        
        with open(os.path.join(extension_dir, "background.js"), 'w', encoding='utf-8') as f:
            f.write(background_js)
            
    def create_popup(self, extension_dir):
        """创建popup页面"""
        popup_html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        .title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .subtitle {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .primary {
            background-color: #4CAF50;
            color: white;
        }
        .secondary {
            background-color: #f0f0f0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
        }
        .success {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
        }
        .info {
            background-color: #e3f2fd;
            color: #1565c0;
        }
        .credentials {
            font-size: 11px;
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            word-break: break-all;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">🔑 微信凭证提取器</div>
        <div class="subtitle">自动提取Cookie和Token</div>
    </div>
    
    <button id="extractBtn" class="button primary">提取凭证</button>
    <button id="copyBtn" class="button secondary">复制到剪贴板</button>
    <button id="sendBtn" class="button secondary">发送到本地应用</button>
    
    <div id="status" class="status info">请在微信公众平台页面使用</div>
    
    <div id="credentials" style="display: none;">
        <div><strong>Cookie:</strong></div>
        <div id="cookieText" class="credentials"></div>
        <div><strong>Token:</strong></div>
        <div id="tokenText" class="credentials"></div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
"""
        
        popup_js = """
// Popup Script
document.addEventListener('DOMContentLoaded', function() {
    const extractBtn = document.getElementById('extractBtn');
    const copyBtn = document.getElementById('copyBtn');
    const sendBtn = document.getElementById('sendBtn');
    const status = document.getElementById('status');
    const credentials = document.getElementById('credentials');
    const cookieText = document.getElementById('cookieText');
    const tokenText = document.getElementById('tokenText');
    
    let currentCredentials = null;
    
    // 提取凭证
    extractBtn.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'extractCredentials'}, function(response) {
                if (response && response.success) {
                    currentCredentials = response.data;
                    showCredentials(response.data);
                    showStatus('✅ 凭证提取成功', 'success');
                } else {
                    showStatus('❌ ' + (response ? response.error : '提取失败'), 'error');
                }
            });
        });
    });
    
    // 复制到剪贴板
    copyBtn.addEventListener('click', function() {
        if (!currentCredentials) {
            showStatus('请先提取凭证', 'error');
            return;
        }
        
        const text = `Cookie: ${currentCredentials.cookie}\\nToken: ${currentCredentials.token}`;
        navigator.clipboard.writeText(text).then(function() {
            showStatus('✅ 已复制到剪贴板', 'success');
        });
    });
    
    // 发送到本地应用
    sendBtn.addEventListener('click', function() {
        if (!currentCredentials) {
            showStatus('请先提取凭证', 'error');
            return;
        }
        
        fetch('http://localhost:8888/credentials', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(currentCredentials)
        }).then(response => {
            if (response.ok) {
                showStatus('✅ 已发送到本地应用', 'success');
            } else {
                showStatus('❌ 发送失败', 'error');
            }
        }).catch(error => {
            showStatus('❌ 本地应用未启动', 'error');
        });
    });
    
    function showCredentials(data) {
        cookieText.textContent = data.cookie;
        tokenText.textContent = data.token;

        // 显示详细信息
        const infoDiv = document.createElement('div');
        infoDiv.style.marginTop = '10px';
        infoDiv.style.fontSize = '12px';
        infoDiv.style.color = '#666';

        let sourceDisplay = data.source || '未知';
        let sourceColor = '#666';

        if (data.source === 'search_request') {
            sourceDisplay = '🎯 搜索公众号请求';
            sourceColor = '#FF5722';
        } else if (data.source === 'network_request') {
            sourceDisplay = '🌐 网络请求';
            sourceColor = '#4CAF50';
        }

        infoDiv.innerHTML = `
            <div><strong>Cookie长度:</strong> ${data.cookie.length} 字符</div>
            <div><strong>Token:</strong> ${data.token}</div>
            <div><strong>来源:</strong> <span style="color: ${sourceColor}">${sourceDisplay}</span></div>
            <div><strong>请求类型:</strong> ${data.requestType || '未知'}</div>
            ${data.searchQuery ? `<div><strong>搜索公众号:</strong> ${data.searchQuery}</div>` : ''}
            <div><strong>时间:</strong> ${new Date(data.timestamp).toLocaleString()}</div>
        `;

        // 移除旧的信息div
        const oldInfo = credentials.querySelector('.credential-info');
        if (oldInfo) {
            oldInfo.remove();
        }

        infoDiv.className = 'credential-info';
        credentials.appendChild(infoDiv);
        credentials.style.display = 'block';

        // 根据Cookie长度和来源显示状态
        if (data.source === 'search_request') {
            if (data.cookie.length > 1000) {
                showStatus('🎯 完美！获取到搜索请求Cookie（长度: ' + data.cookie.length + '）', 'success');
            } else {
                showStatus('🎯 搜索请求Cookie可能不完整（长度: ' + data.cookie.length + '）', 'warning');
            }
        } else if (data.cookie.length > 1000) {
            showStatus('✅ 获取到完整Cookie（长度: ' + data.cookie.length + '）', 'success');
        } else if (data.cookie.length > 500) {
            showStatus('⚠️ Cookie可能不完整（长度: ' + data.cookie.length + '）', 'warning');
        } else {
            showStatus('❌ Cookie长度过短（长度: ' + data.cookie.length + '）', 'error');
        }
    }
    
    function showStatus(message, type) {
        status.textContent = message;
        status.className = `status ${type}`;
    }
    
    // 加载最新凭证
    chrome.storage.local.get(['latestCredentials'], function(result) {
        if (result.latestCredentials) {
            currentCredentials = result.latestCredentials;
            showCredentials(result.latestCredentials);
            showStatus('已加载最新凭证', 'info');
        }
    });
});
"""
        
        with open(os.path.join(extension_dir, "popup.html"), 'w', encoding='utf-8') as f:
            f.write(popup_html)
            
        with open(os.path.join(extension_dir, "popup.js"), 'w', encoding='utf-8') as f:
            f.write(popup_js)
            
    def create_icons(self, extension_dir):
        """创建图标文件（简单的文本图标）"""
        # 这里创建简单的文本文件作为占位符
        # 实际使用时应该使用真正的PNG图标
        icon_sizes = [16, 48, 128]
        
        for size in icon_sizes:
            icon_content = f"Icon {size}x{size} - 微信凭证提取器"
            with open(os.path.join(extension_dir, f"icon{size}.png"), 'w') as f:
                f.write(icon_content)
                
    def create_zip(self, extension_dir, zip_path):
        """打包扩展为zip文件"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(extension_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, extension_dir)
                    zipf.write(file_path, arc_name)
                    
    def show_install_guide(self):
        """显示安装指南"""
        guide = """浏览器扩展安装指南

1. 打开Chrome浏览器
2. 访问 chrome://extensions/
3. 开启"开发者模式"（右上角开关）
4. 点击"加载已解压的扩展程序"
5. 选择生成的扩展目录
6. 扩展安装完成！

使用方法：
1. 访问 https://mp.weixin.qq.com/
2. 登录微信公众平台
3. 点击浏览器工具栏中的扩展图标
4. 点击"提取凭证"按钮
5. 凭证会自动提取并可以复制或发送到本地应用

注意事项：
• 确保在微信公众平台页面使用
• 需要先登录才能提取凭证
• 可以配合本地服务自动接收凭证"""

        messagebox.showinfo("安装指南", guide)
        
    def start_local_server(self):
        """启动本地服务"""
        try:
            import threading
            import http.server
            import socketserver
            import json
            from urllib.parse import urlparse, parse_qs
            
            class CredentialHandler(http.server.BaseHTTPRequestHandler):
                def do_POST(self):
                    if self.path == '/credentials':
                        content_length = int(self.headers['Content-Length'])
                        post_data = self.rfile.read(content_length)
                        
                        try:
                            credentials = json.loads(post_data.decode('utf-8'))
                            
                            # 这里可以处理接收到的凭证
                            print(f"收到凭证: Cookie长度={len(credentials.get('cookie', ''))}, Token={credentials.get('token', '')}")
                            
                            # 保存到文件或发送到主应用
                            self.save_credentials(credentials)
                            
                            self.send_response(200)
                            self.send_header('Content-type', 'application/json')
                            self.send_header('Access-Control-Allow-Origin', '*')
                            self.end_headers()
                            self.wfile.write(b'{"status": "success"}')
                            
                        except Exception as e:
                            self.send_response(400)
                            self.end_headers()
                            self.wfile.write(f'{{"error": "{str(e)}"}}'.encode())
                    else:
                        self.send_response(404)
                        self.end_headers()
                        
                def do_OPTIONS(self):
                    self.send_response(200)
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    self.end_headers()
                    
                def save_credentials(self, credentials):
                    # 保存凭证到临时文件
                    with open('auto_extracted_credentials.json', 'w', encoding='utf-8') as f:
                        json.dump(credentials, f, indent=2, ensure_ascii=False)

                    # 自动保存到凭证池
                    self.save_to_credential_pool(credentials)

                def save_to_credential_pool(self, credentials):
                    """自动保存凭证到凭证池"""
                    try:
                        import json
                        import os
                        from datetime import datetime

                        pool_file = "credential_pool.json"

                        # 读取现有凭证池
                        if os.path.exists(pool_file):
                            with open(pool_file, 'r', encoding='utf-8') as f:
                                pool_data = json.load(f)
                        else:
                            pool_data = {'credentials': [], 'current_index': 0}

                        # 创建新凭证条目
                        new_credential = {
                            'name': f"自动提取_{datetime.now().strftime('%m%d_%H%M')}",
                            'cookie': credentials['cookie'],
                            'token': credentials['token'],
                            'created_time': credentials['timestamp'],
                            'use_count': 0,
                            'invalid': False,
                            'auto_extracted': True,
                            'source': 'browser_extension'
                        }

                        # 检查是否已存在相同的凭证（避免重复）
                        existing = False
                        for cred in pool_data['credentials']:
                            if cred.get('token') == credentials['token']:
                                # 更新现有凭证
                                cred.update(new_credential)
                                existing = True
                                break

                        if not existing:
                            # 添加新凭证
                            pool_data['credentials'].append(new_credential)

                        # 保存凭证池
                        with open(pool_file, 'w', encoding='utf-8') as f:
                            json.dump(pool_data, f, indent=2, ensure_ascii=False)

                        print(f"✅ 凭证已自动保存到凭证池: {new_credential['name']}")

                    except Exception as e:
                        print(f"❌ 保存到凭证池失败: {str(e)}")
                        
                def log_message(self, format, *args):
                    # 禁用默认日志
                    pass
            
            def start_server():
                with socketserver.TCPServer(("", 8888), CredentialHandler) as httpd:
                    httpd.serve_forever()
                    
            server_thread = threading.Thread(target=start_server, daemon=True)
            server_thread.start()
            
            self.log_message("✅ 本地服务已启动在端口8888")
            self.log_message("浏览器扩展可以自动发送凭证到此服务")
            self.status_var.set("本地服务运行中...")
            
        except Exception as e:
            self.log_message(f"❌ 启动本地服务失败: {str(e)}")
            messagebox.showerror("启动失败", f"启动本地服务失败: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = BrowserExtensionHelper(root)
    root.mainloop()

if __name__ == "__main__":
    main()
