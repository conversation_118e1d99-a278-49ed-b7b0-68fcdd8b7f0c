# 增强型 Markdown 转换器

基于 `2MD_good_3.py` 开发的增强版本，集成了 Microsoft MarkItDown 功能，支持多种文件格式转换为 Markdown。

## 功能特性

### 🌐 网页转换
- 支持微信公众号文章转换
- 支持通用网页内容转换
- 自动提取标题和内容
- 支持批量 URL 处理

### 📄 文件格式支持
通过集成 MarkItDown，支持以下文件格式：

- **文档类**: PDF, DOCX, PPTX, XLSX, TXT, MD
- **网页类**: HTML, HTM
- **数据类**: CSV, JSON, XML
- **图书类**: EPUB
- **图片类**: JPG, JPEG, PNG, GIF, BMP, TIFF
- **音频类**: MP3, WAV, M4A, FLAC, OGG
- **其他类**: ZIP, MSG, IPYNB

### 🎨 图形界面
- 直观的 GUI 界面
- 支持文件拖拽选择
- 实时转换进度显示
- 批量处理支持

### ⚙️ 高级功能
- 内容过滤（关键词过滤）
- 多线程并发处理
- 自定义输出目录
- 配置文件保存

## 安装依赖

```bash
# 基础依赖
pip install markdownify beautifulsoup4 lxml requests

# MarkItDown 完整功能
pip install markitdown[all]
```

## 使用方法

### 1. 图形界面模式（推荐）

```bash
python enhanced_markdown_converter.py --gui
```

或者直接运行：

```bash
python enhanced_markdown_converter.py
```

#### GUI 使用步骤：
1. 选择输入类型（URL 或文件）
2. 添加要转换的内容：
   - **URL 模式**: 在文本框中输入 URL（每行一个）
   - **文件模式**: 点击"添加文件"或"添加文件夹"选择文件
3. 设置输出目录
4. 调整并发线程数（可选）
5. 点击"开始转换"

### 2. 命令行模式

#### 转换单个文件：
```bash
python enhanced_markdown_converter.py --file "path/to/document.pdf" --output "./output"
```

#### 转换单个 URL：
```bash
python enhanced_markdown_converter.py --url "https://example.com/article" --output "./output"
```

#### 命令行参数：
- `--gui`: 启动图形界面
- `--file`: 指定要转换的文件路径
- `--url`: 指定要转换的 URL
- `--output`: 输出目录（默认: ./articles）
- `--config`: 自定义配置文件路径
- `--workers`: 并发线程数（默认: 10）

## 配置文件

程序会自动创建 `enhanced_converter_config.json` 配置文件，支持以下设置：

```json
{
  "paragraph_keywords": ["广告", "推广", "赞助"],
  "image_hashes": [],
  "skip_ads": false,
  "skip_promotions": false
}
```

### 配置说明：
- `paragraph_keywords`: 要过滤的段落关键词列表
- `image_hashes`: 要过滤的图片哈希值列表
- `skip_ads`: 是否跳过广告内容
- `skip_promotions`: 是否跳过推广内容

## 输出结构

转换后的文件将保存在指定输出目录的 `MD文档` 子文件夹中：

```
output_directory/
└── MD文档/
    ├── document1.md
    ├── document2.md
    └── ...
```

## 支持的输入源

### 网页 URL
- 微信公众号文章
- 普通网页
- 支持 HTTP/HTTPS 协议

### 本地文件
- 支持 18+ 种文件格式
- 自动格式检测
- 批量处理支持

## 测试功能

运行测试脚本验证功能：

```bash
python test_enhanced_converter.py
```

测试包括：
- 基本功能测试
- 文件格式支持测试
- 文件转换测试
- URL 转换测试
- 内容过滤测试

## 注意事项

1. **MarkItDown 依赖**: 确保安装了 `markitdown[all]` 以获得完整功能
2. **网络连接**: URL 转换需要网络连接
3. **文件权限**: 确保对输出目录有写入权限
4. **大文件处理**: 大文件转换可能需要较长时间
5. **音频转换**: 音频文件转换需要 ffmpeg（可选）

## 错误处理

- 程序会自动跳过无法转换的文件
- 网络错误会自动重试
- 详细的错误信息会显示在控制台

## 与原版对比

相比 `2MD_good_3.py`，增强版本新增：

✅ **新增功能**:
- 支持 18+ 种文件格式
- 图形用户界面
- MarkItDown 集成
- 更好的错误处理
- 配置文件支持

✅ **保留功能**:
- 微信文章转换
- 内容过滤
- 多线程处理
- 批量处理

## 技术架构

- **核心转换**: MarkItDown + markdownify
- **网页解析**: BeautifulSoup4
- **GUI 框架**: tkinter
- **并发处理**: ThreadPoolExecutor
- **配置管理**: JSON

## 许可证

本项目基于原 `2MD_good_3.py` 开发，继承其许可证条款。
