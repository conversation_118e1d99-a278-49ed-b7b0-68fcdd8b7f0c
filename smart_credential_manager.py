#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能凭证管理器
统一管理凭证的获取、验证、轮换和监控
"""

import json
import os
import time
import threading
from datetime import datetime, timedelta
import requests

class SmartCredentialManager:
    def __init__(self, pool_file="credential_pool.json"):
        self.pool_file = pool_file
        self.current_credential = None
        self.last_validation_time = None
        self.validation_interval = 300  # 5分钟验证一次
        self.auto_switch_enabled = True
        
    def load_pool(self):
        """加载凭证池"""
        try:
            if not os.path.exists(self.pool_file):
                return {'credentials': [], 'current_index': 0}
                
            with open(self.pool_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载凭证池失败: {str(e)}")
            return {'credentials': [], 'current_index': 0}
            
    def save_pool(self, pool_data):
        """保存凭证池"""
        try:
            with open(self.pool_file, 'w', encoding='utf-8') as f:
                json.dump(pool_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存凭证池失败: {str(e)}")
            return False
            
    def add_credential(self, cookie, token, name=None, source="manual"):
        """添加凭证到池中"""
        pool_data = self.load_pool()
        
        if not name:
            name = f"{source}_{datetime.now().strftime('%m%d_%H%M')}"
            
        new_credential = {
            'name': name,
            'cookie': cookie,
            'token': token,
            'created_time': datetime.now().isoformat(),
            'use_count': 0,
            'invalid': False,
            'source': source,
            'last_used': None,
            'last_validated': None
        }
        
        # 检查是否已存在相同Token的凭证
        for i, cred in enumerate(pool_data['credentials']):
            if cred.get('token') == token:
                # 更新现有凭证
                pool_data['credentials'][i] = new_credential
                self.save_pool(pool_data)
                return f"更新现有凭证: {name}"
                
        # 添加新凭证
        pool_data['credentials'].append(new_credential)
        self.save_pool(pool_data)
        return f"添加新凭证: {name}"
        
    def get_next_credential(self):
        """获取下一个可用凭证"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        if not credentials:
            return None
            
        # 过滤有效凭证
        valid_credentials = [cred for cred in credentials if not cred.get('invalid', False)]
        if not valid_credentials:
            return None
            
        # 获取当前索引
        current_index = pool_data.get('current_index', 0)
        if current_index >= len(valid_credentials):
            current_index = 0
            
        # 选择当前凭证
        current_cred = valid_credentials[current_index]
        
        # 更新使用统计
        current_cred['use_count'] = current_cred.get('use_count', 0) + 1
        current_cred['last_used'] = datetime.now().isoformat()
        
        # 轮换到下一个凭证
        next_index = (current_index + 1) % len(valid_credentials)
        pool_data['current_index'] = next_index
        
        # 保存更新
        self.save_pool(pool_data)
        
        self.current_credential = current_cred
        return current_cred
        
    def validate_credential(self, credential):
        """验证单个凭证"""
        try:
            from wechat_spider_gui import WeChatSpider
            spider = WeChatSpider(credential['cookie'], credential['token'])
            is_valid, message = spider.test_credentials()
            
            # 更新验证时间
            credential['last_validated'] = datetime.now().isoformat()
            
            if not is_valid:
                credential['invalid'] = True
                
            return is_valid, message
            
        except Exception as e:
            return False, str(e)
            
    def validate_all_credentials(self):
        """验证所有凭证"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        results = []
        for cred in credentials:
            is_valid, message = self.validate_credential(cred)
            results.append({
                'name': cred.get('name', '未命名'),
                'valid': is_valid,
                'message': message
            })
            
        # 保存更新后的状态
        self.save_pool(pool_data)
        return results
        
    def get_pool_status(self):
        """获取凭证池状态"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        if not credentials:
            return {
                'total': 0,
                'valid': 0,
                'invalid': 0,
                'auto_extracted': 0,
                'manual': 0,
                'current': None
            }
            
        valid_count = len([c for c in credentials if not c.get('invalid', False)])
        invalid_count = len(credentials) - valid_count
        auto_count = len([c for c in credentials if c.get('source') == 'browser_extension'])
        manual_count = len(credentials) - auto_count
        
        current_index = pool_data.get('current_index', 0)
        current_name = None
        if current_index < len(credentials):
            current_name = credentials[current_index].get('name', '未命名')
            
        return {
            'total': len(credentials),
            'valid': valid_count,
            'invalid': invalid_count,
            'auto_extracted': auto_count,
            'manual': manual_count,
            'current': current_name,
            'credentials': credentials
        }
        
    def cleanup_invalid_credentials(self):
        """清理无效凭证"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        # 保留有效凭证
        valid_credentials = [cred for cred in credentials if not cred.get('invalid', False)]
        
        removed_count = len(credentials) - len(valid_credentials)
        
        pool_data['credentials'] = valid_credentials
        pool_data['current_index'] = 0  # 重置索引
        
        self.save_pool(pool_data)
        return removed_count
        
    def auto_refresh_credentials(self):
        """自动刷新凭证（需要配合浏览器扩展）"""
        # 这个功能需要浏览器扩展配合
        # 可以定期检查是否有新的自动提取的凭证
        pass
        
    def get_credential_for_spider(self):
        """为爬虫获取最佳凭证"""
        # 如果当前凭证仍然有效，继续使用
        if self.current_credential:
            # 检查是否需要验证
            last_validated = self.current_credential.get('last_validated')
            if last_validated:
                last_time = datetime.fromisoformat(last_validated)
                if datetime.now() - last_time < timedelta(seconds=self.validation_interval):
                    return self.current_credential
                    
            # 验证当前凭证
            is_valid, _ = self.validate_credential(self.current_credential)
            if is_valid:
                return self.current_credential
                
        # 获取下一个凭证
        return self.get_next_credential()
        
    def mark_credential_invalid(self, token):
        """标记凭证为无效"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        for cred in credentials:
            if cred.get('token') == token:
                cred['invalid'] = True
                cred['invalid_time'] = datetime.now().isoformat()
                break
                
        self.save_pool(pool_data)
        
    def get_usage_statistics(self):
        """获取使用统计"""
        pool_data = self.load_pool()
        credentials = pool_data.get('credentials', [])
        
        if not credentials:
            return {}
            
        stats = {
            'total_usage': sum(cred.get('use_count', 0) for cred in credentials),
            'most_used': max(credentials, key=lambda x: x.get('use_count', 0)),
            'least_used': min(credentials, key=lambda x: x.get('use_count', 0)),
            'recent_additions': []
        }
        
        # 最近添加的凭证（24小时内）
        now = datetime.now()
        for cred in credentials:
            created_time = cred.get('created_time')
            if created_time:
                created = datetime.fromisoformat(created_time)
                if now - created < timedelta(hours=24):
                    stats['recent_additions'].append(cred)
                    
        return stats

# 全局实例
credential_manager = SmartCredentialManager()

def main():
    """测试函数"""
    manager = SmartCredentialManager()
    
    # 测试获取状态
    status = manager.get_pool_status()
    print("凭证池状态:", status)
    
    # 测试获取凭证
    cred = manager.get_next_credential()
    if cred:
        print(f"获取到凭证: {cred.get('name')}")
    else:
        print("没有可用凭证")

if __name__ == "__main__":
    main()
