# 微信公众号文章爬虫GUI版本使用说明

## 概述

这是一个基于 `wechatfuben.py` 改进的图形界面版本的微信公众号文章爬虫工具。该工具可以通过微信公众平台的Cookie和Token来获取指定公众号的所有文章列表，并导出为Excel文件。

## 功能特点

- ✅ 图形化界面，操作简单直观
- ✅ 支持通过公众号昵称或FakeID两种方式搜索
- ✅ 实时显示爬取进度和日志信息
- ✅ 自动翻页获取所有历史文章
- ✅ 导出Excel文件，包含标题、链接和发布时间
- ✅ **智能停止功能** - 可随时停止爬取并保存已获取的文章
- ✅ 错误处理和用户友好的提示信息
- ✅ **自动保存配置** - 记住上次输入的Cookie、Token等信息
- ✅ **配置管理** - 支持保存、清除和重新加载配置

## 安装依赖

在运行程序前，请确保安装了所需的Python包：

```bash
pip install -r requirements.txt
```

主要依赖包：
- `tkinter` (Python内置，无需安装)
- `requests` - HTTP请求
- `pandas` - 数据处理
- `openpyxl` - Excel文件操作

## 获取Cookie和Token

### 1. 登录微信公众平台

访问 [https://mp.weixin.qq.com](https://mp.weixin.qq.com) 并登录你的微信公众号管理账户。

### 2. 获取Cookie

1. 在浏览器中按 `F12` 打开开发者工具
2. 切换到 `Network` (网络) 标签
3. 在公众平台页面进行任意操作（如刷新页面）
4. 在网络请求中找到任意一个请求
5. 在请求头中找到 `Cookie` 字段，复制完整的Cookie值

### 3. 获取Token

Token通常在URL中或者网络请求的参数中：

**方法1：从URL获取**
- 在公众平台的URL中查找类似 `token=1234567890` 的参数

**方法2：从网络请求获取**
- 在开发者工具的Network标签中
- 查看任意API请求的参数
- 找到 `token` 参数的值

## 使用步骤

### 1. 启动程序

```bash
python wechat_spider_gui.py
```

### 2. 填写必要信息

1. **Cookie**: 粘贴从浏览器获取的完整Cookie字符串
2. **Token**: 输入从URL或网络请求中获取的Token值
3. **搜索方式**: 选择使用"公众号昵称"或"FakeID"进行搜索
4. **公众号昵称/FakeID**: 
   - 如果选择昵称方式：输入完整的公众号名称（必须完全匹配）
   - 如果选择FakeID方式：输入已知的FakeID
5. **输出目录**: 选择Excel文件的保存位置（默认为桌面）

### 3. 配置管理（新功能）

程序现在支持自动保存和管理配置：

**自动保存**：
- 开始爬取时自动保存当前配置
- 程序关闭时自动保存配置
- 下次启动时自动加载上次的配置

**手动操作**：
- **保存配置**：手动保存当前输入的所有信息
- **清除配置**：清空所有输入框并删除保存的配置文件
- **重新加载配置**：从配置文件重新加载之前保存的信息

### 4. 开始爬取

点击"开始爬取"按钮，程序将：
1. 自动保存当前配置（方便下次使用）
2. 验证输入的Cookie和Token是否有效
3. 搜索指定的公众号并获取FakeID（如果使用昵称搜索）
4. 逐页获取所有文章信息
5. 实时显示进度和状态信息
6. 完成后自动保存为Excel文件

### 5. 智能停止功能（新增）

**停止按钮的工作原理**：
- 点击"停止"按钮后，程序会在下一个检查点停止爬取
- **不会立即中断**：程序会等待当前页面请求完成
- **保存已获取数据**：停止后会自动保存已经获取到的文章
- **文件名标识**：停止保存的文件会添加 `_stopped` 后缀

**停止时机**：
- 在获取每一页文章之前检查停止信号
- 在网络请求过程中定期检查（每0.1秒）
- 在页面间延时期间检查停止信号

**停止后的处理**：
- 自动保存已获取的文章到Excel文件
- 显示已获取的文章数量
- 恢复界面按钮状态
- 在日志中显示停止信息

### 6. 查看结果

爬取完成后，Excel文件将包含以下列：
- **标题**: 文章标题
- **链接**: 文章链接
- **发布时间**: 文章发布时间

**文件命名规则**：
- 正常完成：`公众号名_articles_20250114_143022.xlsx`
- 用户停止：`公众号名_articles_20250114_143022_stopped.xlsx`
- 出错保存：`公众号名_articles_20250114_143022_partial.xlsx`

## 配置文件说明

程序会在同目录下创建 `wechat_gui_config.json` 配置文件，包含：

```json
{
  "cookie": "您的Cookie信息",
  "token": "您的Token信息",
  "search_method": "nickname",
  "nickname_or_fakeid": "公众号名称",
  "output_dir": "输出目录路径"
}
```

**注意**：
- 配置文件包含敏感信息，请妥善保管
- 如需在不同电脑间迁移，可复制此配置文件
- Cookie和Token有时效性，过期后需重新获取

## 注意事项

### ⚠️ 重要提醒

1. **合法使用**: 本工具仅供技术研究和学习使用，请勿用于非法采集
2. **遵守规则**: 请遵守微信公众平台的使用条款和robots.txt规则
3. **频率控制**: 程序内置了请求延时，避免过于频繁的请求
4. **Cookie有效期**: Cookie和Token有时效性，失效后需要重新获取

### 🔧 故障排除

**问题1: "Cookie/Token 已过期或无效"**
- 解决方案: 重新登录微信公众平台，获取新的Cookie和Token

**问题2: "未找到公众号"**
- 解决方案: 
  - 确认公众号昵称输入完全正确（区分大小写）
  - 尝试使用FakeID方式搜索
  - 确认该公众号确实存在且可搜索

**问题3: 程序无响应**
- 解决方案: 
  - 检查网络连接
  - 等待程序完成当前操作
  - 如需要可点击"停止"按钮

**问题4: Excel文件保存失败**
- 解决方案:
  - 确认输出目录有写入权限
  - 关闭可能正在使用的同名Excel文件
  - 检查磁盘空间是否充足

## 技术特点

### 基于最佳实践

本GUI版本基于项目中最稳定的 `wechatfuben.py` 版本，具有：

- **精确匹配**: 公众号昵称搜索使用精确匹配，避免误匹配
- **错误处理**: 完善的异常处理和用户友好的错误提示
- **进度反馈**: 实时显示爬取进度和状态信息
- **请求控制**: 内置延时机制，避免请求过于频繁
- **数据完整**: 获取文章标题、链接和发布时间等完整信息

### 界面设计

- **直观操作**: 简洁明了的图形界面
- **实时反馈**: 日志区域实时显示操作状态
- **灵活配置**: 支持自定义输出目录和搜索方式
- **安全停止**: 可随时停止正在进行的爬取任务

## 版本信息

- **版本**: 1.0
- **基于**: wechatfuben.py
- **开发日期**: 2025年1月14日
- **Python版本**: 3.7+
- **GUI框架**: tkinter

## 免责声明

本工具仅供技术研究和学习使用，使用者应当：

1. 遵守相关法律法规和平台使用条款
2. 不得用于商业用途或大规模数据采集
3. 承担使用本工具产生的一切后果和责任
4. 尊重内容创作者的知识产权

开发者不对使用本工具造成的任何损失或法律问题承担责任。
