#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试配置保存和加载功能
"""

import json
import os

def test_config_save_load():
    """测试配置文件的保存和加载"""
    config_file = "wechat_gui_config.json"
    
    # 测试数据
    test_config = {
        'cookie': 'test_cookie_value',
        'token': 'test_token_value',
        'search_method': 'nickname',
        'nickname_or_fakeid': '测试公众号',
        'output_dir': 'C:\\Users\\<USER>\\Desktop'
    }
    
    try:
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2, ensure_ascii=False)
        print("✅ 配置保存成功")
        
        # 加载配置
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        print("✅ 配置加载成功")
        
        # 验证数据
        if loaded_config == test_config:
            print("✅ 配置数据验证通过")
        else:
            print("❌ 配置数据验证失败")
            
        # 显示配置内容
        print("\n📋 配置内容:")
        for key, value in loaded_config.items():
            print(f"  {key}: {value}")
            
        # 清理测试文件
        if os.path.exists(config_file):
            os.remove(config_file)
            print("\n🧹 测试文件已清理")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    print("🧪 开始测试配置功能...")
    test_config_save_load()
    print("🎉 测试完成！")
