增强型 Markdown 转换器

这是一个基于 2MD_good_3.py 开发的增强版本，集成了 Microsoft MarkItDown 功能。

主要特性：
1. 支持多种文件格式转换
2. 提供图形用户界面
3. 支持批量处理
4. 内容过滤功能

支持的文件格式包括：
- PDF 文档
- Office 文档（Word, Excel, PowerPoint）
- 网页文件（HTML）
- 图片文件
- 音频文件
- 压缩文件
- 等等...

使用方法：
python enhanced_markdown_converter.py --gui

或者命令行模式：
python enhanced_markdown_converter.py --file "document.pdf"

这个工具可以帮助您快速将各种格式的文档转换为 Markdown 格式，
便于文档管理、内容分析和进一步处理。
