# 微信爬虫GUI新增功能说明

## 🎉 新增功能：配置记忆功能

为了提升用户体验，GUI版本新增了**自动保存和加载配置**的功能，用户不再需要每次启动程序都重新输入Cookie、Token等信息。

## 📋 功能详情

### 1. 自动保存配置
- **启动爬取时自动保存**：点击"开始爬取"按钮时，程序会自动保存当前所有输入的信息
- **程序关闭时自动保存**：关闭程序时会自动保存当前配置，确保信息不丢失
- **配置文件位置**：`wechat_gui_config.json`（与程序在同一目录）

### 2. 自动加载配置
- **启动时自动加载**：程序启动后会自动加载上次保存的配置
- **智能填充**：自动填充所有输入框的内容，包括：
  - Cookie信息
  - Token信息
  - 搜索方式（昵称/FakeID）
  - 公众号昵称或FakeID
  - 输出目录路径

### 3. 手动配置管理
新增了三个配置管理按钮：

#### 🔄 保存配置
- **功能**：手动保存当前输入的所有信息
- **使用场景**：修改配置后想立即保存，而不等到开始爬取

#### 🗑️ 清除配置
- **功能**：清空所有输入框并删除配置文件
- **使用场景**：
  - 切换到新的微信账号
  - 清除敏感信息
  - 重新开始配置

#### 🔃 重新加载配置
- **功能**：从配置文件重新加载之前保存的信息
- **使用场景**：
  - 手动修改了输入框后想恢复到保存的状态
  - 配置文件被外部修改后重新加载

## 📁 配置文件格式

配置文件 `wechat_gui_config.json` 采用JSON格式：

```json
{
  "cookie": "您的完整Cookie字符串",
  "token": "您的Token值",
  "search_method": "nickname",
  "nickname_or_fakeid": "公众号名称或FakeID",
  "output_dir": "C:\\Users\\<USER>\\Desktop"
}
```

## 🔒 安全性说明

### 敏感信息保护
- **本地存储**：配置文件仅保存在本地，不会上传到任何服务器
- **明文存储**：Cookie和Token以明文形式存储，请确保计算机安全
- **访问权限**：建议设置配置文件的访问权限，防止其他用户读取

### 建议做法
1. **定期更新**：Cookie和Token有时效性，建议定期更新
2. **安全存储**：不要将配置文件分享给他人
3. **及时清理**：不使用时可以使用"清除配置"功能清理敏感信息

## 🚀 使用流程

### 首次使用
1. 启动程序
2. 输入Cookie、Token等信息
3. 点击"开始爬取"（自动保存配置）
4. 完成爬取任务

### 后续使用
1. 启动程序（自动加载上次配置）
2. 检查配置是否正确
3. 如需修改，直接修改后点击"保存配置"
4. 点击"开始爬取"

### 切换账号
1. 点击"清除配置"按钮
2. 输入新的Cookie、Token等信息
3. 点击"开始爬取"（保存新配置）

## 🔧 技术实现

### 核心方法
- `load_config()`: 加载配置文件
- `save_config()`: 保存当前配置
- `clear_config()`: 清除配置

### 错误处理
- 配置文件损坏时会显示警告并使用默认值
- 保存失败时会在日志中显示错误信息
- 加载失败时程序仍能正常启动

### 兼容性
- 支持中文路径和文件名
- 兼容Windows路径格式
- 自动处理JSON编码问题

## 📈 用户体验提升

### 便利性
- ✅ 无需重复输入长串Cookie和Token
- ✅ 记住常用的公众号名称和输出目录
- ✅ 一键恢复之前的配置

### 效率提升
- ✅ 减少每次启动的配置时间
- ✅ 避免输入错误导致的重新配置
- ✅ 支持快速切换不同配置

### 安全性
- ✅ 本地存储，不涉及网络传输
- ✅ 支持一键清除敏感信息
- ✅ 配置文件格式清晰，便于管理

## 🔄 版本更新

### 当前版本：v1.1
- ✅ 新增配置记忆功能
- ✅ 优化界面布局
- ✅ 增强错误处理
- ✅ 改进用户体验

### 向后兼容
- 旧版本用户升级后可直接使用
- 不影响现有的爬取功能
- 配置功能为可选功能，不使用也不影响正常操作

---

**注意**：此功能旨在提升用户体验，请合理使用并注意保护个人隐私信息。
