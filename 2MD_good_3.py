import os
import requests
import markdownify
from bs4 import BeautifulSoup
import re
import unicodedata
import time
import json
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Optional, Dict, Union
from pathlib import Path
from dataclasses import dataclass, field
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import webbrowser
from urllib.parse import urlparse

# 尝试导入 markitdown，如果不存在则提示安装
try:
    from markitdown import MarkItDown
    MARKITDOWN_AVAILABLE = True
except ImportError:
    MARKITDOWN_AVAILABLE = False
    print("警告: markitdown 库未安装。请运行 'pip install markitdown[all]' 来启用多格式文件转换功能。")

@dataclass
class ContentFilterConfig:
    """内容过滤配置"""
    paragraph_keywords: List[str] = field(default_factory=list)
    image_hashes: List[str] = field(default_factory=list)
    skip_ads: bool = False
    skip_promotions: bool = False

class WeChatArticleDownloader:
    CONFIG_FILE = "wechat_downloader_config.json"

    @staticmethod
    def hash_byte_data(byte_data: bytes) -> str:
        import hashlib
        return hashlib.sha256(byte_data).hexdigest()

    @staticmethod
    def remove_nonvisible_chars(text: str) -> str:
        return ''.join(c for c in text if (unicodedata.category(c) != 'Cn'
                                           and c not in (' ', '\n', '\r')))

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self.CONFIG_FILE
        self.filter_config = self._load_config()
        self.session = requests.Session()  # 使用Session复用连接
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.processed_titles = set()  # 缓存已处理标题
        self.lock = threading.Lock()  # 用于线程安全的文件写入和标题检查

    def _load_config(self) -> ContentFilterConfig:
        default_config = ContentFilterConfig()
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    return ContentFilterConfig(
                        paragraph_keywords=config_data.get('paragraph_keywords', []),
                        image_hashes=config_data.get('image_hashes', []),
                        skip_ads=config_data.get('skip_ads', False),
                        skip_promotions=config_data.get('skip_promotions', False)
                    )
        except Exception as e:
            print(f"加载配置文件失败，将使用默认配置: {str(e)}")
        return default_config

    def _save_config(self) -> None:
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'paragraph_keywords': self.filter_config.paragraph_keywords,
                    'image_hashes': self.filter_config.image_hashes,
                    'skip_ads': self.filter_config.skip_ads,
                    'skip_promotions': self.filter_config.skip_promotions
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")

    def input_urls(self) -> List[Dict]:
        """从文件或输入读取URL"""
        print("请输入文章URL（每行一个，输入空行结束），或输入文件路径：")
        first_input = input().strip()
        urls = []
        if first_input and os.path.isfile(first_input):
            try:
                with open(first_input, 'r', encoding='utf-8') as f:
                    urls = [{
                        'account': 'unknown_account',
                        'title': '',
                        'url': line.strip(),
                        'date': ''
                    } for line in f if line.strip()]
                print(f"从文件 {first_input} 读取了 {len(urls)} 个URL")
            except Exception as e:
                print(f"读取文件失败: {str(e)}")
        else:
            urls.append({
                'account': 'unknown_account',
                'title': '',
                'url': first_input,
                'date': ''
            })
            while True:
                url = input().strip()
                if not url:
                    break
                urls.append({
                    'account': 'unknown_account',
                    'title': '',
                    'url': url,
                    'date': ''
                })
        return urls

    def filter_content(self, text: str) -> str:
        if not self.filter_config:
            return text
        if self.filter_config.paragraph_keywords:
            text = self._filter_paragraphs(text, self.filter_config.paragraph_keywords)
        return text

    def _filter_paragraphs(self, text: str, keywords: List[str]) -> str:
        lines = [line.strip() for line in text.split('\n')]
        filtered_lines = []
        current_paragraph = []

        for line in lines:
            if not line.strip():
                if not self._paragraph_contains_keywords(current_paragraph, keywords):
                    filtered_lines.extend(current_paragraph)
                current_paragraph = []
            else:
                current_paragraph.append(line)

        if not self._paragraph_contains_keywords(current_paragraph, keywords):
            filtered_lines.extend(current_paragraph)

        return '\n\n'.join(filtered_lines) + '\n\n'

    def _paragraph_contains_keywords(self, paragraph: List[str], keywords: List[str]) -> bool:
        paragraph_text = ' '.join(paragraph)
        return any(keyword in paragraph_text for keyword in keywords)

    def convert_to_markdown(self, url: str, title: str, create_time: str,
                            content_soup: BeautifulSoup, account_dir: str) -> tuple:
        # 移除所有图片标签
        for img in content_soup.find_all('img'):
            img.decompose()

        # 转换为 markdown 格式
        markdown_content = markdownify.markdownify(str(content_soup))
        markdown_content = '\n'.join([line + '\n' for line in markdown_content.split('\n') if line.strip()])
        clean_title = self.remove_nonvisible_chars(title)

        markdown = f'# {clean_title}\n\n{create_time}\n\n{markdown_content}\n'
        markdown = re.sub('\xa0{1,}', '\n', markdown, flags=re.UNICODE)
        markdown = re.sub(r'\]\(http([^)]*)\)',
                          lambda x: '](http' + x.group(1).replace(' ', '%20') + ')',
                          markdown)

        return self.filter_content(markdown), clean_title

    def get_title_with_retry(self, url: str, max_retries: int = 3) -> tuple:
        retries = 0
        while retries < max_retries:
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()

                soup = BeautifulSoup(response.text, 'lxml')
                title_element = soup.find('h1', id="activity-name")
                if not title_element:
                    title_element = soup.find('h2', class_="rich_media_title") or \
                                    soup.find('h1', class_="article-title")

                if title_element:
                    title = title_element.text.strip()
                    if title:
                        return title, soup

                raise AttributeError("Title element not found")

            except Exception as e:
                retries += 1
                error_msg = str(e)
                if retries < max_retries:
                    print(f"Retrying ({retries}/{max_retries}) for URL {url}: Error - {error_msg}")
                    time.sleep(2 ** retries)
                else:
                    print(f"Failed to retrieve title for URL {url} after {max_retries} retries. Last error: {error_msg}")
                    return None, None

    def process_url(self, url_data: Dict, output_base: str) -> bool:
        url = url_data['url']
        account_dir = os.path.join(output_base, "MD文档")

        title, soup = self.get_title_with_retry(url)
        if not title or not soup:
            return False

        clean_title = self.remove_nonvisible_chars(title)
        filename_base = re.sub(r'[\\/*?:"<>|]', '', clean_title)
        filepath = os.path.join(account_dir, f"{filename_base}.md")

        with self.lock:
            if filename_base in self.processed_titles or os.path.exists(filepath):
                print(f'标题 "{filename_base}" 已存在，放弃转换该网址: {url}')
                return False
            self.processed_titles.add(filename_base)

        content_soup = soup.find('div', {'class': 'rich_media_content'})
        if not content_soup:
            return False

        markdown, clean_title = self.convert_to_markdown(url, title, "", content_soup, account_dir)

        with self.lock:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown)
            print(f'Processed: MD文档 - {filename_base}.md')
        return True

    def run(self, output_dir: str = "./articles", max_workers: int = 10):
        """运行下载器，使用多线程处理"""
        urls_data = self.input_urls()
        if not urls_data:
            print("没有输入URL")
            return

        os.makedirs(os.path.join(output_dir, "MD文档"), exist_ok=True)

        success_count = 0
        print(f"开始处理 {len(urls_data)} 个URL，使用 {max_workers} 个工作线程...")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self.process_url, url_data, output_dir) for url_data in urls_data]
            for future in as_completed(futures):
                if future.result():
                    success_count += 1

        print(f"\n处理完成: 共{len(urls_data)}条, 成功{success_count}条")

def main():
    parser = argparse.ArgumentParser(description='微信公众号文章下载器')
    parser.add_argument('-o', '--output', default='./articles',
                        help='输出目录 (默认: ./articles)')
    parser.add_argument('--config', help='自定义配置文件路径')
    parser.add_argument('--workers', type=int, default=10,
                        help='并发工作线程数 (默认: 10)')

    args = parser.parse_args()

    downloader = WeChatArticleDownloader(args.config)
    downloader.run(output_dir=args.output, max_workers=args.workers)

if __name__ == "__main__":
    main()
