#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
凭证监控和管理工具
实时监控凭证状态，提供智能提醒
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from datetime import datetime, timedelta
import json
import os

class CredentialMonitor:
    def __init__(self, root):
        self.root = root
        self.root.title("凭证状态监控器")
        self.root.geometry("500x400")
        self.root.resizable(True, True)
        
        self.monitoring = False
        self.monitor_thread = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 凭证状态监控器", 
                               font=('Microsoft YaHei', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="当前状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_var = tk.StringVar(value="未开始监控")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                font=('Microsoft YaHei', 10))
        status_label.pack()
        
        self.last_check_var = tk.StringVar(value="上次检查: 从未")
        last_check_label = ttk.Label(status_frame, textvariable=self.last_check_var,
                                    font=('Microsoft YaHei', 9), foreground='gray')
        last_check_label.pack()
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_button = ttk.Button(button_frame, text="开始监控", 
                                      command=self.start_monitoring)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止监控", 
                                     command=self.stop_monitoring, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="立即检查", 
                  command=self.check_credentials_now).pack(side=tk.LEFT)
        
        # 设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="监控设置", padding="10")
        settings_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 检查间隔
        interval_frame = ttk.Frame(settings_frame)
        interval_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(interval_frame, text="检查间隔:").pack(side=tk.LEFT)
        self.interval_var = tk.StringVar(value="300")  # 5分钟
        interval_entry = ttk.Entry(interval_frame, textvariable=self.interval_var, width=10)
        interval_entry.pack(side=tk.LEFT, padx=(5, 5))
        ttk.Label(interval_frame, text="秒").pack(side=tk.LEFT)
        
        # 自动提醒
        self.auto_remind_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="凭证过期时自动提醒", 
                       variable=self.auto_remind_var).pack(anchor=tk.W)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        from tkinter import scrolledtext
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=60)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            self.status_var.set("🟢 监控中...")
            self.log_message("开始监控凭证状态")
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            
    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            
            self.status_var.set("🔴 已停止监控")
            self.log_message("停止监控凭证状态")
            
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 检查凭证状态
                self.check_credentials()
                
                # 等待指定间隔
                interval = int(self.interval_var.get())
                for _ in range(interval):
                    if not self.monitoring:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.log_message(f"监控过程中发生错误: {str(e)}")
                time.sleep(30)  # 出错后等待30秒再继续
                
    def check_credentials_now(self):
        """立即检查凭证"""
        threading.Thread(target=self.check_credentials, daemon=True).start()
        
    def check_credentials(self):
        """检查凭证状态"""
        try:
            # 尝试从配置文件读取凭证
            config_file = "wechat_gui_config.json"
            if not os.path.exists(config_file):
                self.log_message("⚠️ 未找到配置文件，请先在主程序中保存配置")
                return
                
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            cookie = config.get('cookie', '')
            token = config.get('token', '')
            
            if not cookie or not token:
                self.log_message("⚠️ 配置文件中缺少Cookie或Token")
                return
                
            # 使用爬虫类检查凭证
            from wechat_spider_gui import WeChatSpider
            spider = WeChatSpider(cookie, token, progress_callback=self.log_message)
            
            is_valid, message = spider.test_credentials()
            
            current_time = datetime.now().strftime("%H:%M:%S")
            self.last_check_var.set(f"上次检查: {current_time}")
            
            if is_valid:
                self.log_message("✅ 凭证状态正常")
                if self.monitoring:
                    self.status_var.set("🟢 监控中 - 凭证有效")
            else:
                self.log_message(f"❌ 凭证已过期: {message}")
                if self.monitoring:
                    self.status_var.set("🔴 监控中 - 凭证过期")
                    
                # 自动提醒
                if self.auto_remind_var.get():
                    self.root.after(0, lambda: self.show_expiry_reminder())
                    
        except Exception as e:
            self.log_message(f"检查凭证时发生错误: {str(e)}")
            
    def show_expiry_reminder(self):
        """显示过期提醒"""
        result = messagebox.askyesno(
            "凭证过期提醒",
            "检测到Cookie/Token已过期！\n\n这是正常现象，不是被封禁。\n\n是否立即打开凭证助手获取新凭证？",
            icon='warning'
        )
        
        if result:
            try:
                import subprocess
                import sys
                subprocess.Popen([sys.executable, "credential_helper.py"])
            except Exception as e:
                messagebox.showerror("启动失败", f"无法启动凭证助手: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = CredentialMonitor(root)
    
    def on_closing():
        if app.monitoring:
            if messagebox.askokcancel("退出", "监控正在运行，确定要退出吗？"):
                app.stop_monitoring()
                root.destroy()
        else:
            root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
