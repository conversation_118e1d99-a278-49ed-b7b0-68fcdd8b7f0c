import traceback
import requests
import pandas as pd

"""
日期：2024年11月28日
公众号：哇凉哇哇凉
声明：本文仅供技术研究，请勿用于非法采集，后果自负。
"""


class WeChatSpider:
    def __init__(self, cookie, token):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/120.0.0.0 Safari/537.36",
            "Cookie": cookie,
        }
        self.base_params = {
            "lang": "zh_CN",
            "f": "json",
            "token": token,
        }

    def get_fakeid(self, nickname, begin=0, count=5):
        """获取公众号的 FakeID"""
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
        params = {
            **self.base_params,
            "action": "search_biz",
            "query": nickname,
            "begin": begin,
            "count": count,
            "ajax": "1",
        }

        try:
            response = self.session.get(search_url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            if "list" in data and data["list"]:
                return data["list"][0].get("fakeid")
            return None
        except Exception as e:
            raise Exception(f"获取公众号{nickname}的fakeid失败: {traceback.format_exc()}")

    def get_articles(self, fakeid, begin=0, count=29):
        """获取公众号的文章列表并翻页"""
        all_articles = []
        while True:
            art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
            params = {
                **self.base_params,
                "query": "",
                "begin": begin,
                "count": count,
                "type": 9,
                "action": "list_ex",
                "fakeid": fakeid,
            }

            try:
                response = self.session.get(art_url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()
                if "app_msg_list" in data:
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link")
                        }
                        for item in data["app_msg_list"]
                    ]
                    all_articles.extend(articles)

                    # 判断是否有下一页
                    if len(data["app_msg_list"]) < count:
                        break  # 如果当前页的文章数少于请求的数量，表示已获取所有文章
                    else:
                        begin += count  # 否则，翻到下一页，继续获取
                else:
                    break
            except Exception as e:
                raise Exception(f"获取fakeid={fakeid}的文章失败: {traceback.format_exc()}")

        return all_articles

    def fetch_articles_by_nickname(self, nickname, begin=0, count=5):
        """通过昵称直接获取文章"""
        fakeid = self.get_fakeid(nickname, begin, count)
        if not fakeid:
            raise ValueError(f"未找到公众号 {nickname} 的 fakeid")
        return self.get_articles(fakeid, begin, count)


def main():
    cookie = "pac_uid=0_dBEpKBPeH4yFp; _qimei_uuid42=1920a140c35100f006225434d31cb7f38362bd3664; _qimei_h38=8f95c30406225434d31cb7f30200000a81920a; pgv_pvid=2747312680; RK=xANhcgG+9l; ptcz=583604042df64ce3d9a62dbae3a5427294c6bd0e4a626f107d2a5290e40989ab; ua_id=XLUnDNaqJ2NkUadRAAAAAO9RpY1ZHKAO_Z1PSl7efoY=; wxuin=39547569653086; mm_lang=zh_CN; suid=user_0_dBEpKBPeH4yFp; _qimei_q32=b2865cc032c3a2e35ad2b18f05234a98; _qimei_q36=c5682cd1899a830172e3de0a30001151920a; _qimei_fingerprint=2f4642bf0769087d5b31b1a307301a9f; rand_info=CAESIBCxFr0XdPLH4zwkucYlThCYj0hdvHQXpeNn0OuNk7HP; slave_bizuin=3551612504; data_bizuin=3551612504; bizuin=3551612504; data_ticket=yqty0foGnoDh7w0HC6GlwrjwLCWS1gAsdzxP228li29/bggolNuC9rqAtE90sFA2; slave_sid=SnIyQTlFZ0ZWZDlRaVdUY1lVT0U2OFlmeUpMUmlLNmNNV3hfeEN3MEJ2MlhKTEhxdlh5b1NBcVYzMEJvVTd3OEpsc3lCUUk3Z0VudWtlaGRwaENrNTBZNkw0NnR2aHJSYlkyUk5OTHZHTkZWSGR4dDh2TDIxNmRRRXdtN0pXVDZQMEIzN1BFUUljWFMxRndI; slave_user=gh_561fbcf46439; xid=bf7f80fa7eae5284ee1dc73252763fd3; _clck=3551612504|1|fxc|0; _clsk=4i2p0n|1751712916602|2|1|mp.weixin.qq.com/weheat-agent/payload/record"
    token = "1154024823"  # 需要填入有效的 token
    nickname = "生信小猫"

    spider = WeChatSpider(cookie, token)

    try:
        articles = spider.fetch_articles_by_nickname(nickname)
        if not articles:
            print(f"未获取到公众号 {nickname} 的任何文章。")
            return

        # 将文章列表转换为 DataFrame
        df = pd.DataFrame(articles)

        # 导出到 Excel 文件
        excel_file = fr"C:\Users\<USER>\Desktop\{nickname}_articles.xlsx"
        df.to_excel(excel_file, index=False)  # 使用 utf-8-sig 以支持中文

        print(f"成功将文章导出到 {excel_file}")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()