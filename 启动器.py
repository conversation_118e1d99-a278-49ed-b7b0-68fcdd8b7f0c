#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信公众号爬虫启动器
提供多种启动选项
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from datetime import datetime

class LauncherApp:
    def __init__(self, root):
        self.root = root
        self.root.title("微信公众号爬虫工具集 v2.0")
        self.root.geometry("700x650")
        self.root.resizable(True, True)
        self.root.minsize(650, 600)

        # 设置窗口图标和样式
        self.setup_style()

        # 设置窗口居中
        self.center_window()

        self.create_widgets()

    def setup_style(self):
        """设置样式"""
        style = ttk.Style()

        # 配置样式
        style.configure('Title.TLabel', font=('Microsoft YaHei', 18, 'bold'))
        style.configure('Subtitle.TLabel', font=('Microsoft YaHei', 10))
        style.configure('Tool.TLabel', font=('Microsoft YaHei', 12, 'bold'))
        style.configure('Desc.TLabel', font=('Microsoft YaHei', 9), foreground='#666666')
        style.configure('Action.TButton', font=('Microsoft YaHei', 10), padding=(20, 8))
        style.configure('Status.TLabel', font=('Microsoft YaHei', 9))

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 700
        height = 650
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """创建界面组件"""
        # 创建主容器和滚动条
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(main_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 布局Canvas和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        self.bind_mousewheel()

        # 在可滚动框架中创建内容
        self.create_content_widgets()

    def bind_mousewheel(self):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        def _bind_to_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)

        def _unbind_from_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")

        self.canvas.bind('<Enter>', _bind_to_mousewheel)
        self.canvas.bind('<Leave>', _unbind_from_mousewheel)

    def create_content_widgets(self):
        """创建主要内容组件"""

        # 头部区域
        header_frame = ttk.Frame(self.scrollable_frame)
        header_frame.pack(fill=tk.X, pady=(0, 25))

        # 标题和副标题
        title_label = ttk.Label(header_frame, text="微信公众号爬虫工具集", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(header_frame, text="专业的微信公众号文章采集解决方案", style='Subtitle.TLabel')
        subtitle_label.pack(pady=(5, 0))

        # 状态栏
        status_frame = ttk.Frame(header_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_var = tk.StringVar(value="系统就绪 - 请选择需要的工具")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, style='Status.TLabel')
        status_label.pack(side=tk.LEFT)

        time_label = ttk.Label(status_frame, text=f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                              style='Status.TLabel')
        time_label.pack(side=tk.RIGHT)

        # 分隔线
        ttk.Separator(self.scrollable_frame, orient='horizontal').pack(fill=tk.X, pady=(0, 20))

        # 工具卡片容器
        cards_frame = ttk.Frame(self.scrollable_frame)
        cards_frame.pack(fill=tk.BOTH, expand=True)

        # 创建工具卡片
        self.create_tool_cards(cards_frame)

        # 底部区域
        self.create_footer(self.scrollable_frame)

    def create_tool_cards(self, parent):
        """创建工具卡片"""
        # 主爬虫工具卡片
        spider_card = self.create_card(parent, "🕷️", "主爬虫工具",
                                      "完整的微信公众号文章爬取工具\n• 支持批量下载文章\n• 智能重试机制\n• 自动保存Excel格式",
                                      "启动主爬虫", self.launch_main_spider, "#4CAF50")
        spider_card.pack(fill=tk.X, pady=(0, 15))

        # 凭证助手卡片
        helper_card = self.create_card(parent, "🔑", "凭证获取助手",
                                      "专门解决Cookie/Token过期问题\n• 详细获取指南\n• 实时验证功能\n• 一键复制到主程序",
                                      "启动凭证助手", self.launch_credential_helper, "#FF9800")
        helper_card.pack(fill=tk.X, pady=(0, 15))

        # 自动获取器卡片
        auto_card = self.create_card(parent, "🤖", "自动凭证获取",
                                    "安全自动获取Cookie/Token\n• 浏览器自动化\n• 剪贴板监控\n• 零手动操作",
                                    "启动自动获取", self.launch_auto_fetcher, "#4CAF50")
        auto_card.pack(fill=tk.X, pady=(0, 15))

        # 浏览器扩展助手卡片
        extension_card = self.create_card(parent, "🧩", "浏览器扩展助手",
                                         "生成Chrome扩展，最安全方案\n• 一键生成扩展\n• 沙盒安全隔离\n• 自动提取凭证",
                                         "生成扩展", self.launch_extension_helper, "#673AB7")
        extension_card.pack(fill=tk.X, pady=(0, 15))

        # 凭证池管理卡片
        pool_card = self.create_card(parent, "🔄", "凭证池管理",
                                    "多凭证轮换使用，延长有效期\n• 支持多个凭证轮换\n• 自动切换凭证\n• 减少单个凭证使用频率",
                                    "启动凭证池", self.launch_credential_pool, "#9C27B0")
        pool_card.pack(fill=tk.X, pady=(0, 15))

        # 自动化演示卡片
        demo_card = self.create_card(parent, "🚀", "自动化演示",
                                    "完整自动化流程演示\n• 端到端自动化\n• 流程可视化\n• 最佳实践展示",
                                    "启动演示", self.launch_automation_demo, "#FF5722")
        demo_card.pack(fill=tk.X, pady=(0, 15))

        # 使用指南卡片
        guide_card = self.create_card(parent, "📖", "使用指南",
                                     "详细的使用说明和问题解答\n• 快速入门教程\n• 常见问题解答\n• 最佳实践建议",
                                     "查看指南", self.show_guide, "#2196F3")
        guide_card.pack(fill=tk.X, pady=(0, 15))

    def create_card(self, parent, icon, title, description, button_text, command, color):
        """创建工具卡片"""
        # 卡片主框架
        card_frame = ttk.LabelFrame(parent, padding="20")

        # 卡片内容容器
        content_frame = ttk.Frame(card_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧图标和标题
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 图标和标题行
        title_frame = ttk.Frame(left_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        icon_label = ttk.Label(title_frame, text=icon, font=('Microsoft YaHei', 20))
        icon_label.pack(side=tk.LEFT, padx=(0, 10))

        title_label = ttk.Label(title_frame, text=title, style='Tool.TLabel')
        title_label.pack(side=tk.LEFT, anchor=tk.W)

        # 描述文本
        desc_label = ttk.Label(left_frame, text=description, style='Desc.TLabel', justify=tk.LEFT)
        desc_label.pack(fill=tk.X, anchor=tk.W)

        # 右侧按钮
        right_frame = ttk.Frame(content_frame)
        right_frame.pack(side=tk.RIGHT, padx=(20, 0))

        action_button = ttk.Button(right_frame, text=button_text, command=command, style='Action.TButton')
        action_button.pack(anchor=tk.CENTER)

        return card_frame

    def create_footer(self, parent):
        """创建底部区域"""
        # 分隔线
        ttk.Separator(parent, orient='horizontal').pack(fill=tk.X, pady=(20, 15))

        # 底部框架
        footer_frame = ttk.Frame(parent)
        footer_frame.pack(fill=tk.X)

        # 提示信息
        tip_frame = ttk.LabelFrame(footer_frame, text="� 使用提示", padding="10")
        tip_frame.pack(fill=tk.X, pady=(0, 15))

        tips = [
            "• 首次使用建议先运行凭证助手获取有效的Cookie和Token",
            "• 如果凭证频繁过期，建议使用凭证池管理进行多凭证轮换",
            "• 系统已增加自动保存功能，每50篇文章自动保存，避免数据丢失",
            "• 如果遇到'Cookie/Token已过期'错误，这是正常现象，不是被封禁",
            "• 所有工具都支持中文界面，操作简单直观"
        ]

        for tip in tips:
            tip_label = ttk.Label(tip_frame, text=tip, style='Desc.TLabel')
            tip_label.pack(anchor=tk.W, pady=1)

        # 底部按钮区域
        button_frame = ttk.Frame(footer_frame)
        button_frame.pack(fill=tk.X)

        # 左侧信息
        info_label = ttk.Label(button_frame, text="微信公众号爬虫工具集 v2.0 | 技术研究专用",
                              style='Status.TLabel')
        info_label.pack(side=tk.LEFT)

        # 右侧按钮
        ttk.Button(button_frame, text="退出程序", command=self.root.quit).pack(side=tk.RIGHT)
        
    def update_status(self, message, color="black"):
        """更新状态信息"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def launch_main_spider(self):
        """启动主爬虫工具"""
        self.update_status("正在启动主爬虫工具...")
        try:
            if os.path.exists("wechat_spider_gui.py"):
                subprocess.Popen([sys.executable, "wechat_spider_gui.py"])
                self.update_status("主爬虫工具已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 wechat_spider_gui.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动主爬虫失败：{str(e)}\n\n请检查Python环境和文件权限")

    def launch_credential_helper(self):
        """启动凭证助手"""
        self.update_status("正在启动凭证助手...")
        try:
            if os.path.exists("credential_helper.py"):
                subprocess.Popen([sys.executable, "credential_helper.py"])
                self.update_status("凭证助手已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 credential_helper.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动凭证助手失败：{str(e)}\n\n请检查Python环境和文件权限")

    def launch_credential_pool(self):
        """启动凭证池管理"""
        self.update_status("正在启动凭证池管理...")
        try:
            if os.path.exists("credential_pool.py"):
                subprocess.Popen([sys.executable, "credential_pool.py"])
                self.update_status("凭证池管理已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 credential_pool.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动凭证池管理失败：{str(e)}\n\n请检查Python环境和文件权限")

    def launch_auto_fetcher(self):
        """启动自动凭证获取器"""
        self.update_status("正在启动自动凭证获取器...")
        try:
            if os.path.exists("auto_credential_fetcher.py"):
                subprocess.Popen([sys.executable, "auto_credential_fetcher.py"])
                self.update_status("自动凭证获取器已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 auto_credential_fetcher.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动自动凭证获取器失败：{str(e)}\n\n请检查Python环境和文件权限")

    def launch_extension_helper(self):
        """启动浏览器扩展助手"""
        self.update_status("正在启动浏览器扩展助手...")
        try:
            if os.path.exists("browser_extension_helper.py"):
                subprocess.Popen([sys.executable, "browser_extension_helper.py"])
                self.update_status("浏览器扩展助手已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 browser_extension_helper.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动浏览器扩展助手失败：{str(e)}\n\n请检查Python环境和文件权限")

    def launch_automation_demo(self):
        """启动自动化演示"""
        self.update_status("正在启动自动化演示...")
        try:
            if os.path.exists("automation_demo.py"):
                subprocess.Popen([sys.executable, "automation_demo.py"])
                self.update_status("自动化演示已启动")
            else:
                self.update_status("启动失败 - 文件不存在")
                messagebox.showerror("文件不存在",
                                   "找不到 automation_demo.py 文件\n\n请确保所有文件都在同一目录下")
        except Exception as e:
            self.update_status("启动失败")
            messagebox.showerror("启动失败", f"启动自动化演示失败：{str(e)}\n\n请检查Python环境和文件权限")

    def show_guide(self):
        """显示使用指南"""
        self.update_status("正在打开使用指南...")
        try:
            if os.path.exists("使用指南.md"):
                # 尝试用默认程序打开markdown文件
                if sys.platform.startswith('win'):
                    os.startfile("使用指南.md")
                elif sys.platform.startswith('darwin'):
                    subprocess.run(['open', "使用指南.md"])
                else:
                    subprocess.run(['xdg-open', "使用指南.md"])
                self.update_status("使用指南已打开")

            else:
                # 如果文件不存在，显示简要指南
                self.update_status("显示内置指南")
                self.show_simple_guide()
        except Exception as e:
            self.update_status("打开失败")
            messagebox.showwarning("打开失败", f"无法打开使用指南文件：{str(e)}\n\n将显示内置简要指南")
            self.show_simple_guide()
            
    def show_simple_guide(self):
        """显示简要指南"""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("微信公众号爬虫使用指南")
        guide_window.geometry("750x600")
        guide_window.resizable(True, True)

        # 设置窗口居中
        guide_window.transient(self.root)
        guide_window.grab_set()

        from tkinter import scrolledtext

        # 主容器
        main_frame = ttk.Frame(guide_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📖 微信公众号爬虫使用指南",
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.pack(pady=(0, 15))

        # 创建标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 快速开始标签页
        quick_frame = ttk.Frame(notebook, padding="15")
        notebook.add(quick_frame, text="🚀 快速开始")

        quick_text = """推荐使用流程：

1️⃣ 首次使用建议流程：
   • 点击"启动凭证助手"
   • 按照助手中的详细步骤获取Cookie和Token
   • 在助手中验证凭证有效性
   • 复制凭证到主程序
   • 启动主爬虫工具开始爬取

2️⃣ 如果已有有效凭证：
   • 直接启动主爬虫工具
   • 输入Cookie和Token
   • 点击"验证凭证"确保有效
   • 选择公众号开始爬取

3️⃣ 系统特色功能：
   • 智能重试机制：自动处理临时失败
   • 会话保持功能：减少凭证过期
   • 详细错误诊断：提供针对性建议
   • 批量下载：支持Excel格式导出"""

        quick_text_widget = scrolledtext.ScrolledText(quick_frame, wrap=tk.WORD, height=15)
        quick_text_widget.pack(fill=tk.BOTH, expand=True)
        quick_text_widget.insert(tk.END, quick_text)
        quick_text_widget.config(state=tk.DISABLED)

        # 常见问题标签页
        faq_frame = ttk.Frame(notebook, padding="15")
        notebook.add(faq_frame, text="❓ 常见问题")

        faq_text = """常见问题解答：

Q: 出现"Cookie/Token已过期"错误怎么办？
A: 这不是被封禁！这是微信的正常安全机制。
   解决方法：使用凭证助手重新获取最新的Cookie和Token

Q: 为什么验证成功但获取文章失败？
A: 这是因为不同API的权限要求不同：
   • 搜索公众号的权限要求较低
   • 获取文章列表的权限要求更高
   • 可能存在会话冲突或Token时效性问题
   解决方法：关闭浏览器中的微信公众平台，重新获取凭证

Q: 如何获取Cookie和Token？
A: 使用凭证助手，里面有详细的图文步骤说明：
   • 登录微信公众平台
   • 按F12打开开发者工具
   • 在Network中找到请求
   • 复制Cookie和Token

Q: 验证凭证失败怎么办？
A: 检查以下几点：
   • 确保完整复制了Cookie（通常很长）
   • 确保Token格式正确（纯数字）
   • 重新登录微信公众平台后再获取
   • 检查网络连接是否正常

Q: 爬取过程中突然失败？
A: 可能原因和解决方法：
   • 凭证在使用过程中过期 → 重新获取凭证
   • 网络连接问题 → 检查网络
   • 微信系统繁忙 → 稍后重试
   • 公众号信息错误 → 确认公众号名称"""

        faq_text_widget = scrolledtext.ScrolledText(faq_frame, wrap=tk.WORD, height=15)
        faq_text_widget.pack(fill=tk.BOTH, expand=True)
        faq_text_widget.insert(tk.END, faq_text)
        faq_text_widget.config(state=tk.DISABLED)

        # 注意事项标签页
        notice_frame = ttk.Frame(notebook, padding="15")
        notebook.add(notice_frame, text="⚠️ 注意事项")

        notice_text = """重要注意事项：

🔒 合法使用：
   • 本工具仅供技术研究和学习使用
   • 请遵守相关法律法规和平台规则
   • 不得用于商业用途或恶意采集

⚡ 使用建议：
   • 避免过于频繁的请求，建议间隔2-3秒
   • 不要在多个地方同时使用同一个账号
   • 获取凭证后尽快使用，避免长时间闲置
   • 定期更新凭证以确保稳定使用

🔐 凭证安全：
   • 不要分享你的Cookie和Token给他人
   • 不要在公共场所或不安全的网络环境下获取凭证
   • 定期更换密码和凭证

📊 数据处理：
   • 妥善保管获取的数据
   • 尊重原创作者的版权
   • 不要传播或滥用获取的内容

🛠️ 技术支持：
   • 遇到问题请先查看详细日志
   • 使用凭证助手验证凭证有效性
   • 确认网络连接和系统环境正常
   • 检查公众号信息是否正确

💡 重要提醒：
   Cookie/Token过期是微信平台的正常安全机制，
   不是被封禁或反爬虫！重新获取即可解决。"""

        notice_text_widget = scrolledtext.ScrolledText(notice_frame, wrap=tk.WORD, height=15)
        notice_text_widget.pack(fill=tk.BOTH, expand=True)
        notice_text_widget.insert(tk.END, notice_text)
        notice_text_widget.config(state=tk.DISABLED)

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="启动凭证助手",
                  command=lambda: [guide_window.destroy(), self.launch_credential_helper()]).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="启动主程序",
                  command=lambda: [guide_window.destroy(), self.launch_main_spider()]).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=guide_window.destroy).pack(side=tk.RIGHT)

def main():
    """主函数"""
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # 可以在这里设置窗口图标
        pass
    except:
        pass

    # 创建应用实例
    launcher_app = LauncherApp(root)

    # 设置关闭事件
    def on_closing():
        launcher_app.update_status("正在退出...")
        root.quit()
        root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
