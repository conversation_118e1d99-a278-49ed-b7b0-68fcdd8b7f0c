# 微信公众号文章爬虫使用指南

## 🚀 快速开始

### 推荐使用方式（最简单）
```bash
python 启动器.py
```
启动器提供了三个工具：
- **主爬虫工具**：完整的文章爬取功能
- **凭证获取助手**：专门帮助获取和验证Cookie/Token
- **使用指南**：详细说明文档

### 传统启动方式
```bash
python wechat_spider_gui.py
```

## 问题解决方案

### 关于"Cookie/Token已过期"错误

**重要提醒**：这个错误**不是被封禁**，而是正常的凭证过期现象！

#### 为什么会出现"验证成功但获取文章失败"？
1. **不同API权限要求不同**：搜索公众号的权限要求较低，获取文章的权限要求更高
2. **会话冲突**：可能在浏览器中同时登录了同一账号
3. **Token时效性**：Token可能有非常短的有效期
4. **微信安全机制**：检测到异常访问模式时会主动使凭证失效

#### 最佳解决方案
1. **使用凭证助手**：`python credential_helper.py`
2. **避免多点登录**：关闭浏览器中的微信公众平台页面
3. **获取后立即使用**：不要让凭证闲置太久
4. **使用增强版爬虫**：新版本有自动重试和会话保持功能

### 2. 获取新的Cookie和Token

#### 方法一：使用程序内置指南
1. 点击程序中的"获取凭证指南"按钮
2. 按照详细步骤操作

#### 方法二：手动获取步骤
1. 打开浏览器，访问：https://mp.weixin.qq.com/
2. 登录你的微信公众号账号
3. 按F12打开开发者工具
4. 切换到"Network"（网络）标签页
5. 在公众平台中进行任意操作（如点击菜单）
6. 找到发送到mp.weixin.qq.com的请求
7. 在请求详情中：
   - 复制"Cookie"字段的完整内容
   - 在URL参数中找到"token"参数的值

### 3. 验证凭证
1. 将获取的Cookie和Token粘贴到程序对应输入框
2. 点击"验证凭证"按钮
3. 确保显示"验证成功"

### 4. 开始爬取
1. 选择搜索方式（公众号昵称或FakeID）
2. 输入目标公众号信息
3. 选择输出目录
4. 点击"开始爬取"

## 🆕 新增功能

### 1. 智能重试机制
- 自动检测凭证过期并重试
- 会话保持和刷新功能
- 网络异常自动恢复

### 2. 凭证获取助手 (credential_helper.py)
- 专门的凭证获取和验证工具
- 详细的图文获取指南
- 实时验证凭证有效性
- 一键复制到主程序

### 3. 增强的错误处理
- 区分不同类型的错误（过期、权限、网络等）
- 针对性的解决建议
- 更友好的错误提示

### 4. 启动前凭证验证
- 开始爬取前自动验证凭证
- 避免无效凭证导致的爬取失败
- 提前发现问题

### 5. 工具集启动器 (启动器.py)
- 统一的工具入口
- 一键启动不同功能
- 简化使用流程

### 6. 增强的日志系统
- 更详细的运行状态信息
- API响应状态显示
- 调试信息输出

## 常见问题解答

### Q: 为什么会出现"Cookie/Token已过期"？
A: 这是正常现象，微信平台的凭证会定期过期，通常几小时到几天不等。

### Q: 更换了Cookie/Token还是不行怎么办？
A: 
1. 确保获取的Cookie和Token是最新的
2. 检查是否完整复制了Cookie（通常很长）
3. 确保Token参数正确
4. 尝试重新登录微信公众平台后再获取

### Q: 如何避免频繁过期？
A: 
1. 不要在多个地方同时使用同一个账号
2. 获取凭证后尽快使用
3. 定期更新凭证

### Q: 验证凭证失败怎么办？
A: 
1. 检查网络连接
2. 确认Cookie和Token格式正确
3. 重新获取最新的凭证
4. 检查是否有特殊字符或换行符

## 技术改进

### 1. 错误处理增强
- 添加了多种错误码的识别
- 更准确的错误信息提示

### 2. 网络请求优化
- 增加了请求头信息
- 改进了超时处理

### 3. 用户体验改进
- 添加了凭证验证功能
- 提供了详细的使用指南

## 注意事项

1. **合法使用**：仅用于技术研究，请遵守相关法律法规
2. **频率控制**：避免过于频繁的请求
3. **数据保护**：妥善保管获取的数据
4. **凭证安全**：不要分享你的Cookie和Token

## 故障排除

如果遇到问题，请按以下顺序检查：

1. 运行测试脚本：`python test_wechat_spider.py`
2. 检查依赖包是否完整安装
3. 验证Cookie和Token是否有效
4. 检查网络连接
5. 查看详细的错误日志

## 联系支持

如果问题仍然存在，请提供：
1. 详细的错误信息
2. 操作步骤
3. 系统环境信息

---

**重要提醒**：Cookie/Token过期是正常现象，不是被封禁。按照指南重新获取即可解决问题。
