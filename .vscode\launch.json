{"version": "0.2.0", "configurations": [{"name": "运行微信爬虫GUI", "type": "python", "request": "launch", "program": "${workspaceFolder}/wechat_spider_gui.py", "console": "integratedTerminal", "python": "D:\\anaconda\\envs\\markitdown_env\\python.exe", "cwd": "${workspaceFolder}", "env": {}, "args": []}, {"name": "运行原版爬虫", "type": "python", "request": "launch", "program": "${workspaceFolder}/wechatfuben.py", "console": "integratedTerminal", "python": "D:\\anaconda\\envs\\markitdown_env\\python.exe", "cwd": "${workspaceFolder}", "env": {}, "args": []}, {"name": "运行文章转换工具", "type": "python", "request": "launch", "program": "${workspaceFolder}/2md.py", "console": "integratedTerminal", "python": "D:\\anaconda\\envs\\markitdown_env\\python.exe", "cwd": "${workspaceFolder}", "env": {}, "args": ["-o", "./articles"]}]}