import os
import requests
import markdownify
from bs4 import BeautifulSoup
import re
import unicodedata
import hashlib
import time
import json
import argparse
import random
from dataclasses import dataclass, field
from typing import List, Optional, Dict
from pathlib import Path


@dataclass
class ContentFilterConfig:
    """内容过滤配置"""
    paragraph_keywords: List[str] = field(default_factory=list)
    image_hashes: List[str] = field(default_factory=list)
    skip_ads: bool = False
    skip_promotions: bool = False


class WeChatArticleDownloader:
    CONFIG_FILE = "wechat_downloader_config.json"

    @staticmethod
    def hash_byte_data(byte_data: bytes) -> str:
        return hashlib.sha256(byte_data).hexdigest()

    @staticmethod
    def remove_nonvisible_chars(text: str) -> str:
        return ''.join(c for c in text if (unicodedata.category(c) != 'Cn'
                                           and c not in (' ', '\n', '\r')))

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self.CONFIG_FILE
        self.filter_config = self._load_config()

    def _load_config(self) -> ContentFilterConfig:
        default_config = ContentFilterConfig()
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    return ContentFilterConfig(
                        paragraph_keywords=config_data.get('paragraph_keywords', []),
                        image_hashes=config_data.get('image_hashes', []),
                        skip_ads=config_data.get('skip_ads', False),
                        skip_promotions=config_data.get('skip_promotions', False)
                    )
        except Exception as e:
            print(f"加载配置文件失败，将使用默认配置: {str(e)}")
        return default_config

    def _save_config(self) -> None:
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'paragraph_keywords': self.filter_config.paragraph_keywords,
                    'image_hashes': self.filter_config.image_hashes,
                    'skip_ads': self.filter_config.skip_ads,
                    'skip_promotions': self.filter_config.skip_promotions
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")

    def input_urls(self) -> List[Dict]:
        """手动输入URL"""
        print("请输入文章URL（每行一个，输入空行结束）:")
        urls = []
        while True:
            url = input().strip()
            if not url:
                break
            urls.append({
                'account': 'unknown_account',
                'title': '',
                'url': url,
                'date': ''
            })
        return urls

    def filter_content(self, text: str) -> str:
        if not self.filter_config:
            return text
        if self.filter_config.paragraph_keywords:
            text = self._filter_paragraphs(text, self.filter_config.paragraph_keywords)
        return text

    def _filter_paragraphs(self, text: str, keywords: List[str]) -> str:
        lines = [line.strip() for line in text.split('\n')]
        filtered_lines = []
        current_paragraph = []

        for line in lines:
            if not line.strip():
                if not self._paragraph_contains_keywords(current_paragraph, keywords):
                    filtered_lines.extend(current_paragraph)
                current_paragraph = []
            else:
                current_paragraph.append(line)

        if not self._paragraph_contains_keywords(current_paragraph, keywords):
            filtered_lines.extend(current_paragraph)

        return '\n\n'.join(filtered_lines) + '\n\n'

    def _paragraph_contains_keywords(self, paragraph: List[str], keywords: List[str]) -> bool:
        paragraph_text = ' '.join(paragraph)
        return any(keyword in paragraph_text for keyword in keywords)

    def download_images(self, soup: BeautifulSoup, account_dir: str) -> None:
        image_folder = os.path.join(account_dir, 'images')
        os.makedirs(image_folder, exist_ok=True)

        for img in soup.find_all('img'):
            img_link = img.get('data-src') or img.get('src')
            if not img_link:
                continue

            img_link = img_link.replace(' ', '%20')

            if not img_link.startswith(('http://', 'https://')):
                img_link = 'https://mp.weixin.qq.com' + img_link

            try:
                with requests.get(img_link, stream=True) as response:
                    response.raise_for_status()
                    file_content = response.content

                    img_hash = self.hash_byte_data(file_content)
                    if img_hash in self.filter_config.image_hashes:
                        continue

                    file_ext = (img.get('data-type') or 'jpg').split('?')[0]
                    filename = f"{img_hash}.{file_ext}"
                    filepath = os.path.join(image_folder, filename)

                    if not os.path.exists(filepath):
                        with open(filepath, 'wb') as f:
                            f.write(file_content)

                    relative_path = f"./images/{filename}"
                    img['data-src'] = relative_path
                    img['src'] = relative_path
            except requests.exceptions.RequestException as e:
                print(f"图片下载失败，URL: {img_link}, 错误: {e}")

    def convert_to_markdown(self, url: str, title: str, create_time: str,
                            content_soup: BeautifulSoup, account_dir: str) -> tuple:
        self.download_images(content_soup, account_dir)
        markdown_content = markdownify.markdownify(str(content_soup))

        markdown_soup = BeautifulSoup(markdown_content, 'html.parser')
        for img in markdown_soup.find_all('img'):
            if img.get('data-src') and not img['data-src'].startswith(('http://', 'https://')):
                img['src'] = img['data-src']

        markdown_content = '\n'.join([line + '\n' for line in markdown_content.split('\n') if line.strip()])
        clean_title = self.remove_nonvisible_chars(title)

        # 移除url链接
        markdown = f'# {clean_title}\n\n{create_time}\n\n{markdown_content}\n'

        markdown = re.sub('\xa0{1,}', '\n', markdown, flags=re.UNICODE)
        markdown = re.sub(r'\]\(http([^)]*)\)',
                          lambda x: '](http' + x.group(1).replace(' ', '%20') + ')',
                          markdown)

        return self.filter_content(markdown), clean_title

    def get_title_with_retry(self, url: str, max_retries: int = 3) -> tuple:
        retries = 0
        while retries < max_retries:
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                }

                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                soup = BeautifulSoup(response.text, 'lxml')
                title_element = soup.find('h1', id="activity-name")
                if not title_element:
                    title_element = soup.find('h2', class_="rich_media_title") or \
                                    soup.find('h1', class_="article-title")

                if title_element:
                    title = title_element.text.strip()
                    if title:
                        return title, soup

                raise AttributeError("Title element not found")

            except Exception as e:
                retries += 1
                error_msg = str(e)
                if retries < max_retries:
                    print(f"Retrying ({retries}/{max_retries}) for URL {url}: Error - {error_msg}")
                    time.sleep(2 ** retries)
                else:
                    print(
                        f"Failed to retrieve title for URL {url} after {max_retries} retries. Last error: {error_msg}")
                    return None, None

    def process_url(self, url_data: Dict, output_base: str) -> bool:
        url = url_data['url']
        # 强制使用统一目录名称
        account_dir = os.path.join(output_base, "MD文档")
        os.makedirs(account_dir, exist_ok=True)

        title, soup = self.get_title_with_retry(url)
        if not title or not soup:
            return False

        # 移除日期处理逻辑
        content_soup = soup.find('div', {'class': 'rich_media_content'})
        if not content_soup:
            return False

        markdown, clean_title = self.convert_to_markdown(
            url, title, "", content_soup, account_dir)  # 移除create_time参数

        # 清理文件名
        filename_base = re.sub(r'[\\/*?:"<>|]', '', clean_title)
        
        # 处理重复文件
        counter = 1
        final_filename = filename_base
        while os.path.exists(os.path.join(account_dir, f"{final_filename}.md")):
            final_filename = f"{filename_base}_{counter}"
            counter += 1

        filepath = os.path.join(account_dir, f"{final_filename}.md")

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(markdown)

        print(f'Processed: MD文档 - {final_filename}.md')
        return True

    def run(self, output_dir: str = "./articles"):
        """运行下载器"""
        urls_data = self.input_urls()
        if not urls_data:
            print("没有输入URL")
            return

        os.makedirs(output_dir, exist_ok=True)

        success_count = 0
        for url_data in urls_data:
            if self.process_url(url_data, output_dir):
                success_count += 1
            delay = random.uniform(3, 8)
            print(f"等待 {delay:.2f} 秒后继续...")
            time.sleep(delay)

        print(f"\n处理完成: 共{len(urls_data)}条, 成功{success_count}条")


def main():
    parser = argparse.ArgumentParser(description='微信公众号文章下载器')
    parser.add_argument('-o', '--output', default='./articles',
                        help='输出目录 (默认: ./articles)')
    parser.add_argument('--config', help='自定义配置文件路径')

    args = parser.parse_args()

    downloader = WeChatArticleDownloader(args.config)
    downloader.run(output_dir=args.output)


if __name__ == "__main__":
    main()