#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试停止功能的模拟脚本
"""

import time
import threading

class MockSpider:
    """模拟爬虫类，用于测试停止功能"""
    
    def __init__(self, progress_callback=None):
        self.progress_callback = progress_callback
        
    def log_progress(self, message):
        if self.progress_callback:
            self.progress_callback(message)
        print(message)
    
    def get_articles(self, fakeid, stop_flag=None):
        """模拟获取文章的过程"""
        all_articles = []
        
        for page in range(1, 11):  # 模拟10页数据
            # 检查停止标志
            if stop_flag and stop_flag():
                self.log_progress(f"⏹️ 在第{page}页时停止了爬取")
                break
                
            self.log_progress(f"正在获取第{page}页文章...")
            
            # 模拟网络请求延时
            for i in range(20):  # 2秒延时，分成20个0.1秒检查
                if stop_flag and stop_flag():
                    self.log_progress(f"⏹️ 在第{page}页请求过程中停止了爬取")
                    return all_articles
                time.sleep(0.1)
            
            # 模拟添加文章
            page_articles = [
                {"标题": f"第{page}页文章{i}", "链接": f"http://example.com/{page}_{i}"}
                for i in range(1, 6)
            ]
            all_articles.extend(page_articles)
            self.log_progress(f"第{page}页获取完成，共{len(page_articles)}篇文章")
            
        return all_articles

def test_stop_function():
    """测试停止功能"""
    print("🧪 开始测试停止功能...")
    
    # 创建停止标志
    stop_flag = False
    
    def should_stop():
        return stop_flag
    
    def progress_callback(message):
        print(f"[进度] {message}")
    
    # 创建模拟爬虫
    spider = MockSpider(progress_callback)
    
    # 在新线程中运行爬取
    articles = []
    def crawl_thread():
        nonlocal articles
        articles = spider.get_articles("test_fakeid", stop_flag=should_stop)
    
    thread = threading.Thread(target=crawl_thread)
    thread.start()
    
    # 等待3秒后停止
    time.sleep(3)
    print("\n⏹️ 3秒后发送停止信号...")
    stop_flag = True
    
    # 等待线程结束
    thread.join()
    
    print(f"\n📊 测试结果:")
    print(f"  - 获取到文章数量: {len(articles)}")
    print(f"  - 停止功能是否生效: {'✅ 是' if len(articles) < 50 else '❌ 否'}")
    print(f"  - 是否保存了部分结果: {'✅ 是' if len(articles) > 0 else '❌ 否'}")

def test_immediate_stop():
    """测试立即停止"""
    print("\n🧪 测试立即停止功能...")
    
    stop_flag = True  # 立即设置停止标志
    
    def should_stop():
        return stop_flag
    
    spider = MockSpider()
    articles = spider.get_articles("test_fakeid", stop_flag=should_stop)
    
    print(f"📊 立即停止测试结果:")
    print(f"  - 获取到文章数量: {len(articles)}")
    print(f"  - 立即停止是否生效: {'✅ 是' if len(articles) == 0 else '❌ 否'}")

if __name__ == "__main__":
    test_stop_function()
    test_immediate_stop()
    print("\n🎉 停止功能测试完成！")
